<template>
  <div class="input-components-demo">
    <h1>🎯 Input 组件系列演示</h1>
    <p>基于 Ant Design 架构的 Input 组件系列</p>

    <!-- 基础 Input -->
    <div class="demo-section">
      <h2>📝 基础 Input</h2>
      <div class="demo-item">
        <label>普通输入框:</label>
        <sp-input
          v-model:value="basicValue"
          placeholder="请输入内容"
        />
        <p>值: {{ basicValue }}</p>
      </div>

      <div class="demo-item">
        <label>带图标的输入框:</label>
        <sp-input
          v-model:value="iconValue"
          placeholder="搜索内容"
          suffix-icon="Search"
        />
        <p>值: {{ iconValue }}</p>
      </div>
    </div>

    <!-- Password 组件 -->
    <div class="demo-section">
      <h2>🔒 Password 组件</h2>
      <div class="demo-item">
        <label>密码输入框:</label>
        <sp-input-password
          v-model:value="passwordValue"
          placeholder="请输入密码"
        />
        <p>值: {{ passwordValue }}</p>
      </div>

      <div class="demo-item">
        <label>禁用切换按钮:</label>
        <sp-input-password
          v-model:value="passwordValue2"
          placeholder="请输入密码"
          :visibility-toggle="false"
        />
        <p>值: {{ passwordValue2 }}</p>
      </div>
    </div>

    <!-- Search 组件 -->
    <div class="demo-section">
      <h2>🔍 Search 组件</h2>
      <div class="demo-item">
        <label>搜索输入框:</label>
        <sp-input-search
          v-model:value="searchValue"
          placeholder="请输入搜索内容"
          @search="handleSearch"
        />
        <p>值: {{ searchValue }}</p>
        <p>搜索历史: {{ searchHistory.join(', ') }}</p>
      </div>

      <div class="demo-item">
        <label>加载状态:</label>
        <sp-input-search
          v-model:value="searchValue2"
          placeholder="搜索中..."
          :loading="isLoading"
          @search="handleSearchWithLoading"
        />
        <p>值: {{ searchValue2 }}</p>
      </div>
    </div>

    <!-- TextArea 组件 -->
    <div class="demo-section">
      <h2>📄 TextArea 组件</h2>
      <div class="demo-item">
        <label>多行文本:</label>
        <sp-textarea
          v-model:value="textareaValue"
          placeholder="请输入多行文本"
          :rows="4"
        />
        <p>值: {{ textareaValue }}</p>
      </div>

      <div class="demo-item">
        <label>字符计数:</label>
        <sp-textarea
          v-model:value="textareaValue2"
          placeholder="最多100个字符"
          :maxlength="100"
          :show-count="true"
          :rows="3"
        />
        <p>值: {{ textareaValue2 }}</p>
      </div>

      <div class="demo-item">
        <label>自动调整高度:</label>
        <sp-textarea
          v-model:value="textareaValue3"
          placeholder="自动调整高度"
          :autosize="{ minRows: 2, maxRows: 6 }"
        />
        <p>值: {{ textareaValue3 }}</p>
      </div>
    </div>

    <!-- 尺寸演示 -->
    <div class="demo-section">
      <h2>📏 尺寸演示</h2>
      <div class="demo-item">
        <label>小尺寸:</label>
        <div style="display: flex; gap: 12px; align-items: center">
          <sp-input
            v-model:value="sizeValue"
            prefixIcon="Copy"
            suffixIcon="Copy"
            placeholder="小尺寸"
            size="small"
          />
          <sp-input-password
            v-model:value="sizeValue"
            placeholder="小尺寸密码"
            size="small"
          />
          <sp-input-search
            :maxlength="100"
            v-model:value="sizeValue"
            placeholder="小尺寸搜索"
            size="small"
          />
        </div>
      </div>

      <div class="demo-item">
        <label>中等尺寸:</label>
        <div style="display: flex; gap: 12px; align-items: center">
          <sp-input
            prefixIcon="Copy"
            suffixIcon="Copy"
            v-model:value="sizeValue"
            placeholder="中等尺寸"
            size="medium"
          />
          <sp-input-password
            v-model:value="sizeValue"
            placeholder="中等尺寸密码"
            size="medium"
          />
          <sp-input-search
            prefixIcon="Copy"
            suffixIcon="Copy"
            v-model:value="sizeValue"
            placeholder="中等尺寸搜索"
            size="medium"
          />
        </div>
      </div>

      <div class="demo-item">
        <label>大尺寸:</label>
        <div style="display: flex; gap: 12px; align-items: center">
          <sp-input
            prefixIcon="Copy"
            suffixIcon="Copy"
            v-model:value="sizeValue"
            placeholder="大尺寸"
            size="large"
          />
          <sp-input-password
            v-model:value="sizeValue"
            placeholder="大尺寸密码"
            size="large"
          />
          <sp-input-search
            v-model:value="sizeValue"
            placeholder="大尺寸搜索"
            size="large"
          />
        </div>
      </div>
    </div>

    <!-- 状态演示 -->
    <div class="demo-section">
      <h2>⚡ 状态演示</h2>
      <div class="demo-item">
        <label>禁用状态:</label>
        <div style="display: flex; gap: 12px; align-items: center">
          <sp-input
            v-model:value="disabledValue"
            placeholder="禁用输入框"
            disabled
          />
          <sp-input-password
            v-model:value="disabledValue"
            placeholder="禁用密码框"
            disabled
          />
          <sp-input-search
            v-model:value="disabledValue"
            placeholder="禁用搜索框"
            disabled
          />
        </div>
      </div>

      <div class="demo-item">
        <label>只读状态:</label>
        <div style="display: flex; gap: 12px; align-items: center">
          <sp-input
            v-model:value="readonlyValue"
            placeholder="只读输入框"
            readonly
          />
          <sp-textarea
            v-model:value="readonlyValue"
            placeholder="只读文本域"
            readonly
            :rows="2"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  // 基础值
  const basicValue = ref('')
  const iconValue = ref('')

  // 密码值
  const passwordValue = ref('')
  const passwordValue2 = ref('')

  // 搜索值
  const searchValue = ref('')
  const searchValue2 = ref('')
  const searchHistory = ref<string[]>([])
  const isLoading = ref(false)

  // 文本域值
  const textareaValue = ref('')
  const textareaValue2 = ref('')
  const textareaValue3 = ref('')

  // 尺寸演示值
  const sizeValue = ref('测试内容')

  // 状态演示值
  const disabledValue = ref('禁用状态的值')
  const readonlyValue = ref('只读状态的值')

  // 搜索处理
  const handleSearch = (value: string | number) => {
    console.log('搜索:', value)
    if (value && !searchHistory.value.includes(String(value))) {
      searchHistory.value.push(String(value))
    }
  }

  // 带加载的搜索处理
  const handleSearchWithLoading = (value: string | number) => {
    console.log('搜索 (带加载):', value)
    isLoading.value = true

    setTimeout(() => {
      isLoading.value = false
      if (value && !searchHistory.value.includes(String(value))) {
        searchHistory.value.push(String(value))
      }
    }, 2000)
  }
</script>

<style scoped>
  .input-components-demo {
    padding: 24px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .demo-section {
    margin-bottom: 48px;
    padding: 24px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    margin: 0 0 24px 0;
    color: #1890ff;
    font-size: 20px;
    font-weight: 600;
  }

  .demo-item {
    margin-bottom: 24px;
  }

  .demo-item:last-child {
    margin-bottom: 0;
  }

  .demo-item label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
  }

  .demo-item p {
    margin: 8px 0 0 0;
    padding: 8px 12px;
    background: #f0f0f0;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #666;
  }

  h1 {
    text-align: center;
    color: #1890ff;
    margin-bottom: 8px;
  }

  p {
    text-align: center;
    color: #666;
    margin-bottom: 32px;
  }
</style>
