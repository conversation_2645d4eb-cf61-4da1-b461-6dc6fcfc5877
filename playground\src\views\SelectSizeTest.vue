<template>
  <div class="select-size-test">
    <h1>🎯 Select 尺寸测试</h1>
    <p>测试多选模式下 Tag 标签是否根据 Select 尺寸正确调整</p>

    <!-- 单选模式对比 -->
    <section class="demo-section">
      <h2>单选模式 (Single Select)</h2>
      <div class="demo-container">
        <div class="size-group">
          <div class="size-item">
            <label>Small:</label>
            <sp-select
              v-model:value="singleValue1"
              :options="basicOptions"
              placeholder="小尺寸"
              size="small"
              style="width: 150px"
            />
          </div>
          <div class="size-item">
            <label>Medium:</label>
            <sp-select
              v-model:value="singleValue2"
              :options="basicOptions"
              placeholder="中等尺寸"
              size="medium"
              style="width: 180px"
            />
          </div>
          <div class="size-item">
            <label>Large:</label>
            <sp-select
              v-model:value="singleValue3"
              :options="basicOptions"
              placeholder="大尺寸"
              size="large"
              style="width: 200px"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- 多选模式对比 -->
    <section class="demo-section">
      <h2>多选模式 (Multiple Select)</h2>
      <div class="demo-container">
        <div class="size-group">
          <div class="size-item">
            <label>Small:</label>
            <TestSelect
              v-model:value="multipleValue1"
              :options="basicOptions"
              placeholder="小尺寸"
              size="small"
              multiple
              style="width: 200px"
            />
          </div>
          <div class="size-item">
            <label>Medium:</label>
            <TestSelect
              v-model:value="multipleValue2"
              :options="basicOptions"
              placeholder="中等尺寸"
              size="medium"
              multiple
              style="width: 220px"
            />
          </div>
          <div class="size-item">
            <label>Large:</label>
            <TestSelect
              v-model:value="multipleValue3"
              :options="basicOptions"
              placeholder="大尺寸"
              size="large"
              multiple
              style="width: 250px"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- 预选值测试 -->
    <section class="demo-section">
      <h2>预选值测试 (Pre-selected Values)</h2>
      <div class="demo-container">
        <div class="size-group">
          <div class="size-item">
            <label>Small (预选):</label>
            <sp-select
              v-model:value="preselectedValue1"
              :options="basicOptions"
              placeholder="小尺寸"
              size="small"
              multiple
              style="width: 200px"
            />
          </div>
          <div class="size-item">
            <label>Medium (预选):</label>
            <sp-select
              v-model:value="preselectedValue2"
              :options="basicOptions"
              placeholder="中等尺寸"
              size="medium"
              multiple
              style="width: 220px"
            />
          </div>
          <div class="size-item">
            <label>Large (预选):</label>
            <sp-select
              v-model:value="preselectedValue3"
              :options="basicOptions"
              placeholder="大尺寸"
              size="large"
              multiple
              style="width: 250px"
            />
          </div>
        </div>
      </div>
    </section>

    <!-- 调试信息 -->
    <section class="demo-section">
      <h2>调试信息 (Debug Info)</h2>
      <div class="debug-info">
        <div class="debug-item">
          <h4>多选值:</h4>
          <code>Small: {{ JSON.stringify(multipleValue1) }}</code>
          <code>Medium: {{ JSON.stringify(multipleValue2) }}</code>
          <code>Large: {{ JSON.stringify(multipleValue3) }}</code>
        </div>
        <div class="debug-item">
          <h4>预选值:</h4>
          <code>Small: {{ JSON.stringify(preselectedValue1) }}</code>
          <code>Medium: {{ JSON.stringify(preselectedValue2) }}</code>
          <code>Large: {{ JSON.stringify(preselectedValue3) }}</code>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import TestSelect from '../components/TestSelect.vue'

  // 基础选项数据
  const basicOptions = [
    { label: '选项一', value: 1 },
    { label: '选项二', value: 2 },
    { label: '选项三', value: 3 },
    { label: '选项四', value: 4 },
    { label: '选项五', value: 5 },
  ]

  // 单选值
  const singleValue1 = ref(1)
  const singleValue2 = ref(2)
  const singleValue3 = ref(null)

  // 多选值
  const multipleValue1 = ref([])
  const multipleValue2 = ref([])
  const multipleValue3 = ref([])

  // 预选值
  const preselectedValue1 = ref([1, 2])
  const preselectedValue2 = ref([2, 3, 4])
  const preselectedValue3 = ref([3, 4, 5])
</script>

<style scoped>
  .select-size-test {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .select-size-test h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .select-size-test > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
  }

  .size-group {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }

  .size-item {
    display: flex;
    align-items: center;
    gap: 15px;
  }

  .size-item label {
    width: 120px;
    font-weight: 500;
    color: #2c3e50;
  }

  .debug-info {
    display: flex;
    gap: 30px;
  }

  .debug-item {
    flex: 1;
  }

  .debug-item h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  .debug-item code {
    display: block;
    padding: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #495057;
    word-break: break-all;
    margin-bottom: 5px;
    font-size: 11px;
  }
</style>
