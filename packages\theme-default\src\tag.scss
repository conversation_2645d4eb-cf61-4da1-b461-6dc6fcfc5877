// ================================
// Speed UI Tag 组件样式
// ================================

@use './common/var.scss' as *;

.sp-tag {
  // 基础样式
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
  line-height: 1;
  border-radius: $border-radius-small;
  border: 1px solid transparent;
  cursor: default;
  box-sizing: border-box;
  transition: all $transition-duration-base $transition-timing-base;
  white-space: nowrap;

  // 尺寸变体
  &--small {
    height: 20px;
    padding: 0 6px;
    font-size: 11px;
  }

  &--medium {
    height: 24px;
    padding: 0 8px;
    font-size: 12px;
  }

  &--large {
    height: 28px;
    padding: 0 10px;
    font-size: 13px;
  }

  // 类型变体 - 默认
  &--default {
    &.sp-tag--filled {
      background-color: #f5f5f5;
      color: #666;
      border-color: #d9d9d9;
    }

    &.sp-tag--outlined {
      background-color: transparent;
      color: #666;
      border-color: #d9d9d9;
    }

    &.sp-tag--light {
      background-color: #fafafa;
      color: #666;
      border-color: transparent;
    }
  }

  // 类型变体 - 主要（使用主题色）
  &--primary {
    &.sp-tag--filled {
      background-color: $color-primary;
      color: white;
      border-color: $color-primary;
    }

    &.sp-tag--outlined {
      background-color: transparent;
      color: $color-primary;
      border-color: $color-primary;
    }

    &.sp-tag--light {
      background-color: $color-primary-lightest;
      color: $color-primary;
      border-color: transparent;
    }
  }

  // 类型变体 - 成功
  &--success {
    &.sp-tag--filled {
      background-color: $color-success;
      color: white;
      border-color: $color-success;
    }

    &.sp-tag--outlined {
      background-color: transparent;
      color: $color-success;
      border-color: $color-success;
    }

    &.sp-tag--light {
      background-color: rgba($color-success, 0.1);
      color: $color-success;
      border-color: transparent;
    }
  }

  // 类型变体 - 警告
  &--warning {
    &.sp-tag--filled {
      background-color: $color-warning;
      color: white;
      border-color: $color-warning;
    }

    &.sp-tag--outlined {
      background-color: transparent;
      color: $color-warning;
      border-color: $color-warning;
    }

    &.sp-tag--light {
      background-color: rgba($color-warning, 0.1);
      color: $color-warning;
      border-color: transparent;
    }
  }

  // 类型变体 - 错误
  &--error {
    &.sp-tag--filled {
      background-color: $color-danger;
      color: white;
      border-color: $color-danger;
    }

    &.sp-tag--outlined {
      background-color: transparent;
      color: $color-danger;
      border-color: $color-danger;
    }

    &.sp-tag--light {
      background-color: rgba($color-danger, 0.1);
      color: $color-danger;
      border-color: transparent;
    }
  }

  // 类型变体 - 信息
  &--info {
    &.sp-tag--filled {
      background-color: $color-info;
      color: white;
      border-color: $color-info;
    }

    &.sp-tag--outlined {
      background-color: transparent;
      color: $color-info;
      border-color: $color-info;
    }

    &.sp-tag--light {
      background-color: rgba($color-info, 0.1);
      color: $color-info;
      border-color: transparent;
    }
  }

  // 状态修饰符
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .sp-tag__close {
      cursor: not-allowed;
    }
  }

  &--checkable {
    cursor: pointer;

    &:hover:not(.sp-tag--disabled) {
      opacity: 0.8;
    }

    &:active:not(.sp-tag--disabled) {
      transform: scale(0.98);
    }
  }

  &--checked {
    &.sp-tag--primary {
      background-color: $color-primary-active;
      border-color: $color-primary-active;
    }
  }

  &--closable {
    padding-right: 4px;
  }

  &--round {
    border-radius: 12px;
  }

  &--bordered {
    // 默认已有边框
  }

  // 子元素
  &__icon {
    margin-right: 4px;
    display: flex;
    align-items: center;
  }

  &__content {
    flex: 1;

    &.sp-tag__truncate {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &__close {
    margin-left: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    opacity: 0.7;
    transition: opacity $transition-duration-base;

    &:hover {
      opacity: 1;
    }

    &-icon {
      font-size: 12px;
    }
  }

  // 自定义颜色支持
  &[style*='--tag-custom-color'] {
    &.sp-tag--filled {
      background-color: var(--tag-custom-color);
      border-color: var(--tag-custom-color);
      color: white;
    }

    &.sp-tag--outlined {
      background-color: transparent;
      color: var(--tag-custom-color);
      border-color: var(--tag-custom-color);
    }

    &.sp-tag--light {
      background-color: color-mix(
        in srgb,
        var(--tag-custom-color) 10%,
        transparent
      );
      color: var(--tag-custom-color);
      border-color: transparent;
    }
  }

  // 焦点样式
  &:focus-visible {
    outline: 2px solid $color-primary;
    outline-offset: 2px;
  }
}
