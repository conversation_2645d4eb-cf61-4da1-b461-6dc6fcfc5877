<template>
  <div class="list-variant-demo">
    <h1>🎯 List 样式变体演示</h1>
    <p>演示 List 组件的默认样式和简洁样式</p>

    <!-- 默认样式 -->
    <section class="demo-section">
      <h2>默认样式 (Default Variant)</h2>
      <div class="demo-container">
        <div class="list-wrapper">
          <sp-list
            v-model:selected-keys="selectedKeys1"
            :selectable="true"
            variant="default"
            style="width: 300px"
          >
            <sp-list-item
              item-key="default1"
              title="选项一"
              description="默认样式的第一个选项"
            />
            <sp-list-item
              item-key="default2"
              title="选项二"
              description="默认样式的第二个选项"
            />
            <sp-list-item
              item-key="default3"
              title="选项三"
              description="默认样式的第三个选项"
            />
          </sp-list>
        </div>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys1) }}</code>
          <p><small>默认样式：左边框 + 主题色背景 + 主题色文字</small></p>
        </div>
      </div>
    </section>

    <!-- 简洁样式 -->
    <section class="demo-section">
      <h2>简洁样式 (Simple Variant)</h2>
      <div class="demo-container">
        <div class="list-wrapper">
          <sp-list
            v-model:selected-keys="selectedKeys2"
            :selectable="true"
            variant="simple"
            style="width: 300px"
          >
            <sp-list-item
              item-key="simple1"
              title="选项一"
              description="简洁样式的第一个选项"
            />
            <sp-list-item
              item-key="simple2"
              title="选项二"
              description="简洁样式的第二个选项"
            />
            <sp-list-item
              item-key="simple3"
              title="选项三"
              description="简洁样式的第三个选项"
            />
          </sp-list>
        </div>
        <div class="demo-info">
          <h4>选中状态:</h4>
          <code>{{ JSON.stringify(selectedKeys2) }}</code>
          <p>
            <small>简洁样式：无分割线，无复选框，选中时只有背景色变化</small>
          </p>
        </div>
      </div>
    </section>

    <!-- 对比展示 -->
    <section class="demo-section">
      <h2>对比展示 (Side by Side)</h2>
      <div class="demo-container">
        <div class="comparison-wrapper">
          <div class="comparison-item">
            <h3>默认样式</h3>
            <sp-list
              v-model:selected-keys="selectedKeys3"
              :selectable="true"
              variant="default"
              style="width: 250px"
            >
              <sp-list-item
                item-key="compare1"
                title="Jack"
              />
              <sp-list-item
                item-key="compare2"
                title="Lucy"
              />
              <sp-list-item
                item-key="compare3"
                title="Disabled"
                disabled
              />
              <sp-list-item
                item-key="compare4"
                title="yiminghe"
              />
            </sp-list>
          </div>
          <div class="comparison-item">
            <h3>简洁样式</h3>
            <sp-list
              v-model:selected-keys="selectedKeys4"
              :selectable="true"
              variant="simple"
              style="width: 250px"
            >
              <sp-list-item
                item-key="simple-compare1"
                title="Jack"
              />
              <sp-list-item
                item-key="simple-compare2"
                title="Lucy"
              />
              <sp-list-item
                item-key="simple-compare3"
                title="Disabled"
                disabled
              />
              <sp-list-item
                item-key="simple-compare4"
                title="yiminghe"
              />
            </sp-list>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'

  const selectedKeys1 = ref<string[]>([])
  const selectedKeys2 = ref<string[]>([])
  const selectedKeys3 = ref<string[]>(['compare2'])
  const selectedKeys4 = ref<string[]>(['simple-compare2'])
</script>

<style scoped>
  .list-variant-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .list-variant-demo h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 10px;
  }

  .list-variant-demo > p {
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 40px;
  }

  .demo-section {
    margin-bottom: 50px;
    padding: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
  }

  .demo-container {
    display: flex;
    gap: 30px;
    align-items: flex-start;
  }

  .list-wrapper {
    flex-shrink: 0;
  }

  .demo-info {
    flex: 1;
    min-width: 250px;
  }

  .demo-info h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
  }

  .demo-info code {
    display: block;
    padding: 10px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    color: #495057;
    word-break: break-all;
    margin-bottom: 8px;
    font-size: 12px;
  }

  .demo-info p {
    margin: 10px 0 0 0;
    color: #6c757d;
  }

  .demo-info small {
    font-size: 12px;
    color: #999;
  }

  .comparison-wrapper {
    display: flex;
    gap: 40px;
    width: 100%;
  }

  .comparison-item {
    flex: 1;
  }

  .comparison-item h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    text-align: center;
  }
</style>
