<!--
  Tag.vue - 用户接口层
  
  职责：
  - 处理用户API和props定义
  - 设置默认值
  - 对外暴露方法
  - 事件透传
-->

<template>
  <TagLogic
    ref="tagLogicRef"
    v-bind="logicProps"
    @close="$emit('close', $event)"
    @click="$emit('click', $event)"
    @check="$emit('check', $event)"
  >
    <!-- 透传所有插槽 -->
    <template
      v-for="(_, name) in $slots"
      #[name]="slotData"
    >
      <slot
        :name="name"
        v-bind="slotData"
      />
    </template>
  </TagLogic>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import TagLogic from './TagLogic.vue'
  import type { TagProps, TagEmits, TagInstance } from './types'
  import { TAG_DEFAULTS } from './constants'

  /**
   * Tag - 用户接口层
   *
   * 特点：
   * 1. 提供完整的用户API
   * 2. 处理默认值设置
   * 3. 对外暴露方法
   * 4. 事件透传
   */

  defineOptions({
    name: 'SpTag',
    inheritAttrs: false,
  })

  // Props定义和默认值
  const props = withDefaults(defineProps<TagProps>(), {
    size: TAG_DEFAULTS.size,
    type: TAG_DEFAULTS.type,
    variant: TAG_DEFAULTS.variant,
    closable: TAG_DEFAULTS.closable,
    disabled: TAG_DEFAULTS.disabled,
    checkable: TAG_DEFAULTS.checkable,
    checked: TAG_DEFAULTS.checked,
    round: TAG_DEFAULTS.round,
    bordered: TAG_DEFAULTS.bordered,
    truncate: TAG_DEFAULTS.truncate,
  })

  // 事件定义
  const emit = defineEmits<TagEmits>()

  // 组件引用
  const tagLogicRef = ref<InstanceType<typeof TagLogic> | null>(null)

  // 计算传递给逻辑层的属性
  const logicProps = computed(() => {
    return {
      ...props,
    }
  })

  // 暴露给父组件的方法
  const focus = () => {
    // 获取 TagInner 的 DOM 元素并聚焦
    const tagInnerEl = tagLogicRef.value?.$el
    if (tagInnerEl && typeof tagInnerEl.focus === 'function') {
      tagInnerEl.focus()
    }
  }

  const blur = () => {
    // 获取 TagInner 的 DOM 元素并失焦
    const tagInnerEl = tagLogicRef.value?.$el
    if (tagInnerEl && typeof tagInnerEl.blur === 'function') {
      tagInnerEl.blur()
    }
  }

  // 暴露实例方法
  defineExpose<TagInstance>({
    focus,
    blur,
  })
</script>

<script lang="ts">
  export default {
    name: 'SpTag',
    inheritAttrs: false,
  }
</script>
