# Speed UI - AI 助手快速参考

## 🚀 常用指令模板

### 创建新组件

```
请为我创建一个新的 {ComponentName} 组件，包含以下功能：
- [功能描述]
- [Props 需求]
- [事件需求]

请遵循 Speed UI 开发规范，包含完整的文件结构、类型定义、测试和文档。
```

### 优化现有组件

```
请优化 {ComponentName} 组件的 [具体方面]，需要：
- 保持向后兼容性
- 遵循 BEM 命名规范
- 增强类型安全
- 补充测试覆盖
```

### 修复样式问题

```
{ComponentName} 组件的样式有问题：[具体描述]
请使用 BEM 规范和 CSS 变量来修复，确保每个属性独占一行。
样式文件应该写在 packages/theme-default/src/ 目录下。
```

### 添加主题支持

```
请为 {ComponentName} 组件添加主题切换支持：
- 添加 bem.s('custom') 主题类名
- 在 packages/theme-default/src/{component}.scss 中添加主题样式
- 使用 --sp-color-primary 等主题变量
- 确保所有变体都支持主题切换
```

## 📋 检查清单

### 组件开发完成检查

- [ ] 使用 `<script setup lang="ts">` 语法
- [ ] 完整的 TypeScript 类型定义
- [ ] BEM 类名系统实现
- [ ] Props 和 Emits 接口定义
- [ ] 单元测试覆盖 > 80%
- [ ] README.md 文档完整
- [ ] CSS 变量使用 `--sp-` 前缀
- [ ] 每个属性独占一行格式
- [ ] 包含 `bem.s('custom')` 主题类名
- [ ] 样式文件在 `packages/theme-default/src/` 目录
- [ ] 支持主题变量 `--sp-color-primary` 等

### 代码审查检查

- [ ] TypeScript 无错误
- [ ] 测试全部通过
- [ ] Prettier 格式化正确
- [ ] ESLint 无警告
- [ ] 文档更新完整
- [ ] 向后兼容性保持

## 🎯 常见任务

### 1. 类名生成优化

使用 ButtonClassBuilder 模式，包含：

- 变体优先级处理
- 形状互斥逻辑
- 状态类名管理
- 调试信息支持

### 2. 测试编写

结构化测试：

```typescript
describe('ComponentName', () => {
  describe('基础功能', () => {})
  describe('Props 测试', () => {})
  describe('事件测试', () => {})
  describe('状态测试', () => {})
})
```

### 3. 文档更新

包含：

- 组件概述
- 基础用法示例
- API 文档（Props/Events/Slots）
- 样式定制说明

## ⚡ 快速命令

### 格式化代码

```bash
npm run format                    # 格式化所有文件
npm run format:button            # 格式化按钮组件
npm run format:check             # 检查格式
```

### 运行测试

```bash
npm test                         # 运行所有测试
npm test button                  # 运行按钮测试
```

### 构建项目

```bash
npm run build                    # 构建所有包
npm run build:ui                 # 只构建 UI 包
```

## 🔧 故障排除

### Prettier 不工作

1. 重启 VS Code
2. 检查 `.prettierrc` 配置
3. 确认 VS Code 扩展已安装
4. 手动运行 `npm run format`

### TypeScript 错误

1. 检查类型导入
2. 确认接口定义完整
3. 运行 `npm run typecheck`

### 测试失败

1. 检查组件 API 变更
2. 更新测试用例
3. 确认 mock 数据正确

## 📝 提示词模板

### 请求代码生成

```
根据 Speed UI 规范，请为我 [具体需求]。
要求：
- 遵循 BEM 命名
- 完整 TypeScript 类型
- 包含测试和文档
- 每个属性独占一行
```

### 请求代码优化

```
请优化这段代码，遵循 Speed UI 规范：
[代码片段]

重点关注：
- 类型安全
- 性能优化
- 代码可读性
- 测试覆盖
```

### 请求问题诊断

```
Speed UI 项目中遇到问题：[问题描述]
请帮我诊断并提供解决方案，确保符合项目规范。
```

---

💡 **提示**: 在与 AI 助手交互时，明确提及"遵循 Speed UI 规范"可以获得更准确的帮助。
