// Tag 组件类型定义

export interface TagProps {
  // === 内容相关 ===
  label?: string                    // 标签文本
  value?: any                       // 标签值
  
  // === 外观控制 ===
  size?: 'small' | 'medium' | 'large'  // 尺寸
  type?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info'  // 类型，primary会使用主题色
  variant?: 'filled' | 'outlined' | 'light'  // 变体
  color?: string                    // 自定义颜色
  
  // === 功能控制 ===
  closable?: boolean               // 是否可关闭
  disabled?: boolean               // 是否禁用
  checkable?: boolean              // 是否可选中
  checked?: boolean                // 是否选中（checkable为true时）
  
  // === 图标 ===
  icon?: string                    // 前置图标
  closeIcon?: string               // 自定义关闭图标
  
  // === 样式 ===
  round?: boolean                  // 圆角样式
  bordered?: boolean               // 是否显示边框
  
  // === 其他 ===
  maxWidth?: string | number       // 最大宽度
  truncate?: boolean               // 文本截断
}

export interface TagEmits {
  (e: 'close', value: any): void           // 关闭事件
  (e: 'click', event: MouseEvent): void    // 点击事件
  (e: 'check', checked: boolean): void     // 选中状态变化（checkable为true时）
}

export interface TagSlots {
  default?: () => any              // 默认插槽（标签内容）
  icon?: () => any                 // 图标插槽
  close?: () => any                // 关闭按钮插槽
}

// Tag 组件实例方法
export interface TagInstance {
  focus: () => void
  blur: () => void
}

// TagInner 组件的属性
export interface TagInnerProps {
  ariaLabel?: string
  role?: string
  ariaChecked?: boolean
  ariaDisabled?: boolean
  tabindex?: number
}

// TagLogic 组件的属性
export interface TagLogicProps extends TagProps {
  // 继承所有 TagProps
}

// TagLogic 组件的事件
export interface TagLogicEmits extends TagEmits {
  // 继承所有 TagEmits
}
