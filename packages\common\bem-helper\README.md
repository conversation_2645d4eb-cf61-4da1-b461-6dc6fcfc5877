# Speed UI BEM 助手工具

一个专为 Speed UI 设计的 BEM 类名生成工具，帮助你快速生成符合 BEM 规范的 CSS 类名。

## 🚀 特性

- 🎯 **专为 Speed UI 优化** - 默认使用 `sp-` 前缀
- 📝 **完整的 TypeScript 支持** - 提供完整的类型定义
- 🔧 **简单易用** - 简化的 API，保留核心功能
- 🎨 **BEM 规范** - 严格遵循 BEM 命名规范
- 🌐 **CSS 变量支持** - 内置 CSS 变量生成功能
- 📦 **轻量级** - 无额外依赖，体积小巧

## 📦 安装

```bash
npm install @speed-ui/bem-helper
# 或
yarn add @speed-ui/bem-helper
# 或
pnpm add @speed-ui/bem-helper
```

## 🔨 基础用法

### 创建 BEM 助手

```typescript
import { useBEM } from '@speed-ui/bem-helper'

// 创建按钮组件的 BEM 助手
const bem = useBEM('button')

// 生成基础类名
bem.b() // 'sp-button'

// 生成元素类名
bem.e('icon') // 'sp-button__icon'
bem.e('text') // 'sp-button__text'

// 生成修饰符类名
bem.m('primary') // 'sp-button--primary'
bem.m('large') // 'sp-button--large'

// 生成元素修饰符类名
bem.em('icon', 'large') // 'sp-button__icon--large'

// 生成状态类名（用于组合组件）
bem.s('group') // 'sp-button-group'
```

### 在 Vue 组件中使用

```vue
<template>
  <button :class="[bem.b(), bem.m(type), { [bem.m('disabled')]: disabled }]">
    <i v-if="icon" :class="bem.e('icon')"></i>
    <span :class="bem.e('text')">
      <slot />
    </span>
  </button>
</template>

<script setup lang="ts"> 
import { useBEM } from '@speed-ui/bem-helper'

interface Props {
  type?: 'primary' | 'secondary'
  disabled?: boolean
  icon?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  disabled: false
})

const bem = useBEM('button')
</script>
```

## 🎨 CSS 变量支持

```typescript
const bem = useBEM('button')

// 生成 CSS 变量名
bem.var('color') // '--sp-button-color'
bem.var('bg-color') // '--sp-button-bg-color'

// 生成 CSS 变量值
bem.getVar('color') // 'var(--sp-button-color)'

// 生成 CSS 变量声明
bem.setVar('color', '#fff') // '--sp-button-color: #fff;'

// 批量生成 CSS 变量
const vars = bem.vars({
  color: '#fff',
  'bg-color': '#007bff',
  'border-radius': '4px'
})
// 结果:
// {
//   '--sp-button-color': '#fff',
//   '--sp-button-bg-color': '#007bff',
//   '--sp-button-border-radius': '4px'
// }

// 全局变量
bem.globalVar('primary-color') // '--sp-primary-color'
bem.getGlobalVar('primary-color') // 'var(--sp-primary-color)'
```

## 🔧 CSS 选择器生成

```typescript
const bem = useBEM('button')

// 生成 CSS 选择器
bem.selector() // '.sp-button'
bem.selectorE('icon') // '.sp-button__icon'
bem.selectorM('primary') // '.sp-button--primary'
```

## 🎯 自定义命名空间

```typescript
import { useCustomBEM } from '@speed-ui/bem-helper'

// 使用自定义命名空间
const bem = useCustomBEM('button', 'my')

bem.b() // 'my-button'
bem.e('icon') // 'my-button__icon'
bem.m('primary') // 'my-button--primary'
```

## 📚 API 参考

### useBEM(block: string)

创建一个使用默认命名空间 (`sp`) 的 BEM 助手。

**参数:**
- `block` - 块名称（如 'button', 'input'）

**返回:** BEM 助手对象

### useCustomBEM(block: string, namespace: string)

创建一个使用自定义命名空间的 BEM 助手。

**参数:**
- `block` - 块名称
- `namespace` - 自定义命名空间前缀

**返回:** BEM 助手对象

### BEM 助手方法

| 方法 | 描述 | 示例 |
|------|------|------|
| `b()` | 生成基础块类名 | `sp-button` |
| `e(element)` | 生成元素类名 | `sp-button__icon` |
| `m(modifier)` | 生成修饰符类名 | `sp-button--primary` |
| `em(element, modifier)` | 生成元素修饰符类名 | `sp-button__icon--large` |
| `s(state)` | 生成状态类名 | `sp-button-group` |
| `selector()` | 生成 CSS 选择器 | `.sp-button` |
| `selectorE(element)` | 生成元素选择器 | `.sp-button__icon` |
| `selectorM(modifier)` | 生成修饰符选择器 | `.sp-button--primary` |
| `var(variable)` | 生成 CSS 变量名 | `--sp-button-color` |
| `getVar(variable)` | 生成 CSS 变量值 | `var(--sp-button-color)` |
| `setVar(variable, value)` | 生成 CSS 变量声明 | `--sp-button-color: #fff;` |
| `vars(object)` | 批量生成 CSS 变量 | `{ '--sp-button-color': '#fff' }` |
| `globalVar(variable)` | 生成全局变量名 | `--sp-primary-color` |
| `getGlobalVar(variable)` | 生成全局变量值 | `var(--sp-primary-color)` |

## 🌟 最佳实践

### 1. 组件级别使用

```typescript
// components/Button/index.vue
const bem = useBEM('button')

// components/Input/index.vue  
const bem = useBEM('input')

// components/Modal/index.vue
const bem = useBEM('modal')
```

### 2. 样式文件中使用

```scss
// button.scss
.sp-button {
  // 基础样式
  
  &__icon {
    // 图标样式
  }
  
  &--primary {
    // 主要按钮样式
  }
  
  &--large {
    // 大尺寸样式
  }
}
```

### 3. CSS 变量管理

```typescript
// 定义主题变量
const themeVars = bem.vars({
  'primary-color': '#007bff',
  'secondary-color': '#6c757d',
  'success-color': '#28a745',
  'warning-color': '#ffc107',
  'error-color': '#dc3545'
})

// 应用到样式中
const style = {
  ...themeVars,
  color: bem.getVar('primary-color')
}
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如果你在使用过程中遇到问题，请：

1. 查看文档和示例
2. 搜索已有的 Issues
3. 创建新的 Issue 描述问题

---

**Speed UI Team** ❤️
