<template>
  <div class="unstyled-demo">
    <h1>Unstyled Components 演示</h1>

    <!-- <PERSON> Button (Unstyled) 演示 -->
    <section class="demo-section">
      <h2>1. Base Button (Unstyled) - 纯逻辑组件</h2>
      <p>只有功能逻辑，没有样式，完全可自定义</p>

      <div class="demo-group">
        <h3>基础功能测试</h3>
        <BaseButton
          @click="handleBaseClick"
          @state-change="handleStateChange"
        >
          无样式按钮
        </BaseButton>

        <BaseButton
          disabled
          @click="handleBaseClick"
        >
          禁用状态
        </BaseButton>

        <BaseButton
          loading
          @click="handleBaseClick"
        >
          加载状态
        </BaseButton>
      </div>

      <div class="demo-group">
        <h3>自定义样式的 Base Button</h3>
        <BaseButton
          class="custom-button-1"
          @click="handleBaseClick"
          @state-change="handleStateChange"
        >
          自定义样式 1
        </BaseButton>

        <BaseButton
          class="custom-button-2"
          @click="handleBaseClick"
        >
          自定义样式 2
        </BaseButton>

        <BaseButton
          class="custom-button-3"
          @click="handleBaseClick"
        >
          自定义样式 3
        </BaseButton>
      </div>
    </section>

    <!-- Styled Button 演示 -->
    <section class="demo-section">
      <h2>2. Styled Button - 基于 Base Button 的样式化组件</h2>
      <p>基于 Base Button，添加了预设的样式系统</p>

      <div class="demo-group">
        <h3>按钮类型</h3>
        <StyledButton
          primary
          @click="handleStyledClick"
        >
          Primary
        </StyledButton>

        <StyledButton
          type="secondary"
          @click="handleStyledClick"
        >
          Secondary
        </StyledButton>

        <StyledButton
          type="danger"
          @click="handleStyledClick"
        >
          Danger
        </StyledButton>

        <StyledButton
          type="ghost"
          @click="handleStyledClick"
        >
          Ghost
        </StyledButton>

        <StyledButton
          type="link"
          @click="handleStyledClick"
        >
          Link
        </StyledButton>
      </div>

      <div class="demo-group">
        <h3>按钮尺寸</h3>
        <StyledButton
          size="small"
          @click="handleStyledClick"
        >
          Small
        </StyledButton>

        <StyledButton
          size="medium"
          @click="handleStyledClick"
        >
          Medium
        </StyledButton>

        <StyledButton
          size="large"
          @click="handleStyledClick"
        >
          Large
        </StyledButton>
      </div>

      <div class="demo-group">
        <h3>按钮状态</h3>
        <StyledButton @click="handleStyledClick">Normal</StyledButton>

        <StyledButton
          disabled
          @click="handleStyledClick"
        >
          Disabled
        </StyledButton>

        <StyledButton
          loading
          loading-text="Loading..."
          @click="handleStyledClick"
        >
          Loading
        </StyledButton>
      </div>

      <div class="demo-group">
        <h3>自定义颜色</h3>
        <StyledButton
          color="#ff6b6b"
          @click="handleStyledClick"
        >
          Custom Red
        </StyledButton>

        <StyledButton
          color="#4ecdc4"
          @click="handleStyledClick"
        >
          Custom Teal
        </StyledButton>

        <StyledButton
          color="#45b7d1"
          @click="handleStyledClick"
        >
          Custom Blue
        </StyledButton>
      </div>
    </section>

    <!-- 状态监控 -->
    <section class="demo-section">
      <h2>3. 状态监控</h2>
      <div class="state-monitor">
        <h3>当前按钮状态:</h3>
        <pre>{{ JSON.stringify(currentState, null, 2) }}</pre>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import BaseButton from '../../../packages/ui/src/components/base-button/base-button.vue'
  import StyledButton from '../../../packages/ui/src/components/button/button.vue'

  const currentState = ref<any>(null)

  const handleBaseClick = (event: MouseEvent) => {
    console.log('Base Button clicked:', event)
  }

  const handleStyledClick = (event: MouseEvent) => {
    console.log('Styled Button clicked:', event)
  }

  const handleStateChange = (state: any) => {
    currentState.value = state
    console.log('Button state changed:', state)
  }
</script>

<style scoped>
  .unstyled-demo {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .demo-section {
    margin-bottom: 3rem;
    padding: 2rem;
    border: 1px solid #e1e8ed;
    border-radius: 8px;
    background: #fafbfc;
  }

  .demo-section h2 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.5rem;
  }

  .demo-section p {
    margin: 0 0 1.5rem 0;
    color: #666;
  }

  .demo-group {
    margin-bottom: 2rem;
  }

  .demo-group h3 {
    margin: 0 0 1rem 0;
    color: #34495e;
    font-size: 1.2rem;
  }

  .demo-group > * + * {
    margin-left: 1rem;
  }

  .state-monitor {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
  }

  .state-monitor h3 {
    margin: 0 0 0.5rem 0;
    color: #495057;
  }

  .state-monitor pre {
    margin: 0;
    font-size: 0.875rem;
    color: #6c757d;
    background: white;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #e9ecef;
  }

  /* 自定义 Base Button 样式 */
  .custom-button-1 {
    background: linear-gradient(45deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  }

  .custom-button-1:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
  }

  .custom-button-1:active {
    transform: translateY(0);
  }

  .custom-button-2 {
    background: transparent;
    color: #4ecdc4;
    border: 2px solid #4ecdc4;
    padding: 10px 20px;
    border-radius: 0;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
  }

  .custom-button-2:hover {
    background: #4ecdc4;
    color: white;
    border-color: #4ecdc4;
  }

  .custom-button-3 {
    background: #2c3e50;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 0;
    font-weight: 300;
    font-size: 18px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .custom-button-3::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  .custom-button-3:hover::before {
    left: 100%;
  }

  .custom-button-3:hover {
    background: #34495e;
  }
</style>
