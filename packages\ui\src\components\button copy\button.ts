/**
 * 按钮变体类型
 */
export type ButtonVariant = 'primary' | 'secondary' | 'dashed' | 'text' | 'link'

/**
 * 按钮颜色类型
 */
export type ButtonColorType = 'default' | 'success' | 'warning' | 'danger'

/**
 * 按钮尺寸类型
 */
export type ButtonSize = 'small' | 'medium' | 'large' | 'huge'

/**
 * 按钮形状类型（互斥）
 */
export type ButtonShape = 'circle' | 'round' | 'square'

/**
 * 提示配置接口
 */
export interface TipConfig {
  text: string
  bgColor?: string
  color?: string
  top?: string
  right?: string
  fontSize?: string
  padding?: string
}

// 按钮属性接口
export interface ButtonProps {
  // 按钮变体（互斥，优先级：primary > secondary > dashed > text > link）
  primary?: boolean // 主按钮
  secondary?: boolean // 次按钮
  dashed?: boolean // 虚线按钮
  text?: boolean // 文本按钮
  link?: boolean // 链接按钮

  // 基础属性
  disabled?: boolean
  loading?: boolean
  loadingIcon?: import('vue').FunctionalComponent // 自定义 loading 图标
  toggleable?: boolean
  attrStyle?: import('vue').CSSProperties // 自定义样式对象

  // 尺寸和外观
  size?: ButtonSize // 按钮尺寸
  type?: ButtonColorType // 颜色类型
  icon?: import('vue').FunctionalComponent // 图标组件对象

  // 形状（互斥，优先级：circle > round > square）
  circle?: boolean // 圆形按钮
  round?: boolean // 椭圆形大圆角按钮
  square?: boolean // 无圆角方形按钮

  // 布局
  vertical?: boolean // 垂直排列

  // 功能特性
  time?: number // 倒计时时间（秒）
  pressDown?: number // 长按时间（毫秒），如 1000 表示按住 1 秒
  tip?: string | TipConfig // 右上角标签文字或配置对象
}

// 事件接口
export interface ButtonEmits {
  click: [event: MouseEvent]
  toggle: [active: boolean]
  countdown: [time: number] // 倒计时事件
  pressStart: [event: MouseEvent] // 长按开始事件
  pressEnd: [event: MouseEvent] // 长按结束事件
  pressComplete: [event: MouseEvent] // 长按完成事件
}
