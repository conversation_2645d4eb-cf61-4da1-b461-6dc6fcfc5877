@use './common/var.scss' as *;

// ===== InputField 基础变量 =====
:root {
  --sp-color-white: #ffffff;
}

// ===== InputField 主样式 =====
.sp-input-field {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;

  // ===== 包装器 =====
  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    transition: all 0.3s ease;
    cursor: text;
  }

  // ===== 浮动标签 =====
  &__label {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: $color-text-secondary;
    font-size: 16px;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
    transform-origin: left top;
    z-index: 1;
    user-select: none;
    cursor: text;
    background: $background-color-base;
    padding: 0 4px;
    border-radius: 4px;
    white-space: nowrap;

    // 必填星号
    &-asterisk {
      color: $color-danger;
      margin-right: 2px;
    }

    // 浮动状态
    &--floating {
      top: 0;
      transform: translateY(-50%) scale(0.85);
      color: $color-text-secondary;
      font-weight: 500;
    }
  }

  // ===== 聚焦状态下的标签颜色 =====
  &--focused {
    .sp-input-field__label {
      color: $color-primary;
    }
  }

  // ===== 输入框内核 =====
  &__inner {
    flex: 1;
    width: 100%;
    padding: 16px 0;
    border: none;
    outline: none;
    background: transparent;
    color: $color-text-primary;
    font-size: 16px;
    transition: all 0.3s ease;

    &::placeholder {
      color: $color-text-secondary;
      opacity: 0.8;
    }

    &:disabled {
      color: $color-text-disabled;
      cursor: not-allowed;

      &::placeholder {
        color: $color-text-disabled;
      }
    }
  }

  // ===== 前缀/后缀区域 =====
  &__prefix,
  &__suffix {
    display: flex;
    align-items: center;
    gap: 6px;
    color: $color-text-secondary;
    flex-shrink: 0;
  }

  &__prefix {
    padding: 16px 6px 16px 12px;
  }

  &__suffix {
    padding: 16px 12px 16px 6px;
  }

  // ===== 图标样式 =====
  &__prefix-icon,
  &__suffix-icon,
  &__clear,
  &__password {
    color: $color-text-secondary;
    transition: color 0.3s ease;

    &:hover {
      color: $color-primary;
    }
  }

  &__clear,
  &__password {
    cursor: pointer;

    &:hover {
      color: $color-primary;
    }
  }

  // ===== 字数统计 =====
  &__count {
    font-size: 12px;
    color: $color-text-secondary;
    white-space: nowrap;
  }

  // ===== 加载动画 =====
  &__loading-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    overflow: hidden;
    background: transparent;
  }

  &__loading-progress {
    height: 100%;
    background: $color-primary;
    width: 30%;
    animation: sp-input-field-loading 1.5s infinite ease-in-out;
  }

  // ===== 消息区域 =====
  &__message,
  &__helper {
    margin-top: 6px;
    font-size: 12px;
    line-height: 1.4;
    transition: all 0.3s ease;
  }

  &__message {
    color: $color-danger;

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  &__helper {
    color: $color-text-secondary;

    &--error {
      color: $color-danger;
    }

    &--warning {
      color: $color-warning;
    }

    &--success {
      color: $color-success;
    }
  }

  // ===== 尺寸变体 =====
  &--small {
    .sp-input-field__inner {
      padding: 12px 10px;
      font-size: 14px;
    }

    .sp-input-field__label {
      left: 10px;
      font-size: 14px;

      &--floating {
        transform: translateY(-45%) scale(0.8);
      }
    }

    .sp-input-field__prefix {
      padding: 12px 5px 12px 10px;
    }

    .sp-input-field__suffix {
      padding: 12px 10px 12px 5px;
    }

    // 小尺寸下划线和填充变体的特殊处理
    &.sp-input-field--underlined {
      .sp-input-field__inner {
        padding: 12px 0;
      }

      .sp-input-field__prefix {
        padding: 12px 5px 12px 0;
      }

      .sp-input-field__suffix {
        padding: 12px 0 12px 5px;
      }

      // 修复：小尺寸下划线变体，当有前缀图标时避免标签重叠
      &.sp-input-field--has-prefix {
        .sp-input-field__label {
          left: 10px; // 小尺寸默认状态下偏移以避免与前缀图标重叠（调整为更合适的距离）

          &--floating {
            left: 0; // 小尺寸浮动状态下回到左边
          }
        }
      }
    }

    &.sp-input-field--filled {
      .sp-input-field__inner {
        padding: 12px 10px;
      }

      .sp-input-field__prefix {
        padding: 12px 5px 12px 10px;
      }

      .sp-input-field__suffix {
        padding: 12px 10px 12px 5px;
      }

      // 小尺寸：当标签浮动时，调整内容区域向下移动
      &.sp-input-field--label-floating {
        .sp-input-field__inner {
          padding-top: 18px; // 小尺寸增加顶部padding
          padding-bottom: 6px;
        }

        .sp-input-field__prefix {
          padding-top: 18px;
          padding-bottom: 6px;
        }

        .sp-input-field__suffix {
          padding-top: 18px;
          padding-bottom: 6px;
        }
      }

      // 修复：小尺寸填充变体，当有前缀图标时避免标签重叠
      &.sp-input-field--has-prefix {
        .sp-input-field__label {
          left: 26px; // 小尺寸默认状态下偏移以避免与前缀图标重叠

          &--floating {
            left: 10px; // 小尺寸浮动状态下偏移
          }
        }
      }
    }
  }

  &--large {
    .sp-input-field__inner {
      padding: 20px 0;
      font-size: 18px;
    }

    .sp-input-field__label {
      left: 14px;
      font-size: 18px;

      &--floating {
        transform: translateY(-55%) scale(0.9);
      }
    }

    .sp-input-field__prefix {
      padding: 20px 7px 20px 14px;
    }

    .sp-input-field__suffix {
      padding: 20px 14px 20px 7px;
    }

    // 大尺寸下划线和填充变体的特殊处理
    &.sp-input-field--underlined {
      .sp-input-field__inner {
        padding: 20px 0;
      }

      .sp-input-field__prefix {
        padding: 20px 7px 20px 0;
      }

      .sp-input-field__suffix {
        padding: 20px 0 20px 7px;
      }

      // 修复：大尺寸下划线变体，当有前缀图标时避免标签重叠
      &.sp-input-field--has-prefix {
        .sp-input-field__label {
          left: 25px; // 大尺寸默认状态下偏移以避免与前缀图标重叠（调整为更合适的距离）

          &--floating {
            left: 0; // 大尺寸浮动状态下回到左边
          }
        }
      }
    }

    &.sp-input-field--filled {
      .sp-input-field__inner {
        padding: 20px 0;
      }

      .sp-input-field__prefix {
        padding: 20px 7px 20px 14px;
      }

      .sp-input-field__suffix {
        padding: 20px 14px 20px 7px;
      }

      // 大尺寸：当标签浮动时，调整内容区域向下移动
      &.sp-input-field--label-floating {
        .sp-input-field__inner {
          padding-top: 26px; // 大尺寸增加顶部padding
          padding-bottom: 14px;
        }

        .sp-input-field__prefix {
          padding-top: 26px;
          padding-bottom: 14px;
        }

        .sp-input-field__suffix {
          padding-top: 26px;
          padding-bottom: 14px;
        }
      }

      // 修复：大尺寸填充变体，当有前缀图标时避免标签重叠
      &.sp-input-field--has-prefix {
        .sp-input-field__label {
          left: 38px; // 大尺寸默认状态下偏移以避免与前缀图标重叠

          &--floating {
            left: 14px; // 大尺寸浮动状态下偏移
          }
        }
      }
    }
  }

  // ===== 变体样式 =====

  // 默认变体
  &--default {
    .sp-input-field__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 6px;
      background: var(--sp-color-white);
    }

    .sp-input-field__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // 下划线变体
  &--underlined {
    .sp-input-field__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 0;
      background: transparent;
    }

    .sp-input-field__label {
      left: 0;
      background: transparent;
      padding: 0;

      &--floating {
        top: -8px;
        transform: translateY(0) scale(0.85);
      }
    }

    .sp-input-field__inner {
      padding: 16px 0;
    }

    .sp-input-field__prefix {
      padding: 16px 6px 16px 0;
    }

    .sp-input-field__suffix {
      padding: 16px 0 16px 6px;
    }

    // 修复：当有前缀图标时，标签需要向右偏移避免重叠
    &.sp-input-field--has-prefix {
      .sp-input-field__label {
        left: 20px; // 默认状态下偏移以避免与前缀图标重叠（调整为更合适的距离）

        &--floating {
          left: 0; // 浮动状态下回到左边，因为已经不会和图标重叠了
        }
      }
    }

    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        border-bottom-color: $color-primary;

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 50%;
          width: 0;
          height: 2px;
          background: $color-primary;
          transition: all 0.3s ease;
          transform: translateX(-50%);
          animation: sp-input-field-underline 0.3s ease forwards;
        }
      }
    }
  }

  // 填充变体 (基于下划线样式 + 背景)
  &--filled {
    .sp-input-field__wrapper {
      border: none;
      border-bottom: 1px solid $border-color-base;
      border-radius: 6px 6px 0 0;
      background: $background-color-hover;
    }

    .sp-input-field__label {
      background: transparent;
      padding: 0;

      &--floating {
        top: 4px; // 标签浮动到背景框内的顶部（调整高度后重新定位）
        transform: translateY(0) scale(0.85);
      }
    }

    .sp-input-field__inner {
      padding: 16px 0;
    }

    .sp-input-field__prefix {
      padding: 16px 6px 16px 12px;
    }

    .sp-input-field__suffix {
      padding: 16px 12px 16px 6px;
    }

    // 当标签浮动时，调整内容区域向下移动为标签留空间
    &.sp-input-field--label-floating {
      .sp-input-field__inner {
        padding-top: 27px; // 增加顶部padding为浮动标签留空间
        padding-bottom: 5px; // 相应减少底部padding保持总高度
      }

      .sp-input-field__prefix {
        padding-top: 27px;
        padding-bottom: 5px;
      }

      .sp-input-field__suffix {
        padding-top: 27px;
        padding-bottom: 5px;
      }
    }

    // 修复：填充变体当有前缀图标时，标签需要向右偏移避免重叠
    &.sp-input-field--has-prefix {
      .sp-input-field__label {
        left: 32px; // 默认状态下偏移以避免与前缀图标重叠

        &--floating {
          left: 12px; // 浮动状态下稍微偏移，因为浮动后在框内顶部
        }
      }
    }

    &:hover {
      .sp-input-field__wrapper {
        background: $background-color-active;
      }
    }

    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        border-bottom-color: $color-primary;

        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 50%;
          width: 0;
          height: 2px;
          background: $color-primary;
          transition: all 0.3s ease;
          transform: translateX(-50%);
          animation: sp-input-field-underline 0.3s ease forwards;
        }
      }
    }
  }

  // 胶囊变体
  &--pill {
    .sp-input-field__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 50px;
      background: var(--sp-color-white);
    }

    .sp-input-field__label {
      background: var(--sp-color-white);
      padding: 0 4px;
      border-radius: 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // 方形变体
  &--square {
    .sp-input-field__wrapper {
      border: 1px solid $border-color-base;
      border-radius: 0;
      background: var(--sp-color-white);
    }

    .sp-input-field__label {
      background: var(--sp-color-white);
      padding: 0 4px;

      &--floating {
        top: 0;
        transform: translateY(-50%) scale(0.85);
      }
    }

    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        border-color: $color-primary;
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2);
      }
    }
  }

  // 无边框变体
  &--unborder {
    .sp-input-field__wrapper {
      border: none;
      border-radius: 6px;
      background: transparent;
    }

    .sp-input-field__label {
      background: transparent;
      padding: 0;

      &--floating {
        top: -8px;
        transform: translateY(0) scale(0.85);
      }
    }

    &:hover {
      .sp-input-field__wrapper {
        background: $background-color-hover;
      }
    }

    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        background: $background-color-active;
      }
    }
  }

  // ===== 状态样式 =====
  &--error {
    .sp-input-field__wrapper {
      border-color: $color-danger !important;
    }

    .sp-input-field__label {
      color: $color-danger !important;
    }

    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2) !important;
      }
    }
  }

  &--disabled {
    .sp-input-field__wrapper {
      background: $background-color-disabled;
      border-color: $border-color-disabled;
      cursor: not-allowed;
    }

    .sp-input-field__label {
      color: $color-text-disabled;
    }
  }

  // ===== 发光效果 =====
  &--effect-glow {
    &.sp-input-field--focused {
      .sp-input-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-primary, 0.2),
          0 4px 12px rgba($color-primary, 0.15);
      }
    }

    &.sp-input-field--error.sp-input-field--focused {
      .sp-input-field__wrapper {
        box-shadow: 0 0 0 1px rgba($color-danger, 0.2),
          0 4px 12px rgba($color-danger, 0.15);
      }
    }
  }
}

// ===== 动画 =====
@keyframes sp-input-field-loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(350%);
  }
}

@keyframes sp-input-field-underline {
  0% {
    width: 0;
  }
  100% {
    width: 100%;
  }
}

// ===== 消息过渡动画 =====
.sp-input-field-message-enter-active,
.sp-input-field-message-leave-active {
  transition: all 0.3s ease;
}

.sp-input-field-message-enter-from,
.sp-input-field-message-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
