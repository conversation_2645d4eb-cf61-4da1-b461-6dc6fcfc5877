// 类名工具函数
export { classNames, getPrefixCls, bemClass } from './classNames'

// 组件基础功能
export {
  type BaseComponentProps,
  type ComponentState,
  type FormItemField,
  useFormField,
  useComponentState,
  generateComponentClasses,
  defaultComponentProps
} from './componentBase'

// 事件处理工具
export {
  type EventHandlers,
  debounce,
  throttle,
  createFocusHandler,
  createBlurHandler,
  createInputHandler,
  createChangeHandler,
  createClickHandler,
  createKeydownHandler,
  createCompositionHandlers,
  createElementOperations,
  EventHandlerFactory
} from './eventHandlers'

// 样式工具函数
export {
  type StyleValue,
  type SizeMap,
  type ThemeColor,
  type ComponentStatus,
  mergeStyles,
  normalizeStyle,
  getSizeValue,
  createSizeStyle,
  getStatusColor,
  getThemeColor,
  createShadowStyle,
  createTransitionStyle,
  createBorderStyle,
  createBorderRadiusStyle,
  createFlexStyle,
  createTextEllipsisStyle,
  createAbsoluteStyle,
  StyleBuilder,
  createStyleBuilder
} from './styleUtils'

// 验证工具函数
export {
  type ValidationRule,
  type ValidationResult,
  ValidationRules,
  validateValue,
  validateSingleRule,
  validateForm,
  isFormValid,
  getFormErrors,
  ValidationRuleBuilder,
  createValidationRules,
  Rules
} from './validation'

// BEM 命名空间工具
export {
  type NamespaceHelper,
  createNamespace,
  useNamespace
} from './namespace'