<template>
  <div class="textarea-field-example">
    <h3>TextareaField 组件示例</h3>
    
    <!-- 基础用法 -->
    <div class="example-section">
      <h4>基础用法</h4>
      <sp-textarea-field
        v-model:value="basicValue"
        label="基础文本域"
        name="basic"
        placeholder="请输入内容..."
        :rows="3"
      />
      <p>值: {{ basicValue }}</p>
    </div>

    <!-- 必填字段 -->
    <div class="example-section">
      <h4>必填字段</h4>
      <sp-textarea-field
        v-model:value="requiredValue"
        label="必填文本域"
        name="required"
        placeholder="这是必填字段..."
        :required="true"
        :rows="4"
      />
    </div>

    <!-- 带字数统计 -->
    <div class="example-section">
      <h4>带字数统计</h4>
      <sp-textarea-field
        v-model:value="countValue"
        label="限制字数"
        name="count"
        placeholder="最多100字..."
        :maxlength="100"
        :show-word-limit="true"
        :rows="3"
      />
    </div>

    <!-- 可清除 -->
    <div class="example-section">
      <h4>可清除</h4>
      <sp-textarea-field
        v-model:value="clearableValue"
        label="可清除文本域"
        name="clearable"
        placeholder="可以清除内容..."
        :clearable="true"
        :rows="3"
      />
    </div>

    <!-- 自动调整高度 -->
    <div class="example-section">
      <h4>自动调整高度</h4>
      <sp-textarea-field
        v-model:value="autosizeValue"
        label="自动高度"
        name="autosize"
        placeholder="高度会自动调整..."
        :autosize="{ minRows: 2, maxRows: 6 }"
      />
    </div>

    <!-- 禁用状态 -->
    <div class="example-section">
      <h4>禁用状态</h4>
      <sp-textarea-field
        v-model:value="disabledValue"
        label="禁用文本域"
        name="disabled"
        placeholder="这是禁用状态..."
        :disabled="true"
        :rows="3"
      />
    </div>

    <!-- 只读状态 -->
    <div class="example-section">
      <h4>只读状态</h4>
      <sp-textarea-field
        v-model:value="readonlyValue"
        label="只读文本域"
        name="readonly"
        :readonly="true"
        :rows="3"
      />
    </div>

    <!-- 帮助文本 -->
    <div class="example-section">
      <h4>帮助文本</h4>
      <sp-textarea-field
        v-model:value="helperValue"
        label="带帮助文本"
        name="helper"
        placeholder="输入一些内容..."
        helper-text="这是帮助文本，用于说明字段用途"
        :rows="3"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import TextareaField from './textarea-field.vue'

// 注册组件
const SpTextareaField = TextareaField

// 响应式数据
const basicValue = ref('')
const requiredValue = ref('')
const countValue = ref('')
const clearableValue = ref('可以清除的内容')
const autosizeValue = ref('这是一段较长的文本，用于测试自动调整高度功能。当内容增加时，文本域的高度会自动调整。')
const disabledValue = ref('这是禁用状态的内容')
const readonlyValue = ref('这是只读状态的内容，不能编辑')
const helperValue = ref('')
</script>

<style scoped>
.textarea-field-example {
  padding: 20px;
  max-width: 600px;
}

.example-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}

.example-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.example-section p {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}
</style>
