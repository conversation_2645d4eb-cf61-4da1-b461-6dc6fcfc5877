<template>
  <div class="simple-tag-test">
    <h1>简单 Tag 测试</h1>

    <div class="test-section">
      <h3>🏷️ 基础 Tag 组件测试</h3>

      <div class="test-item">
        <label>单个 Tag：</label>
        <Tag
          size="small"
          closable
          @close="handleTagClose"
        >
          测试标签
        </Tag>
      </div>

      <div class="test-item">
        <label>多个 Tag：</label>
        <div class="tags-container">
          <Tag
            v-for="tag in testTags"
            :key="tag.id"
            size="small"
            closable
            @close="removeTag(tag.id)"
          >
            {{ tag.label }}
          </Tag>
        </div>
      </div>

      <div class="test-item">
        <label>空状态多选 Select（测试无选项时的宽度）：</label>
        <sp-select
          v-model:value="emptyValues"
          :options="options"
          placeholder="请选择"
          multiple
          clearable
          style="width: 250px"
        />
        <div class="debug-info">选中值: {{ JSON.stringify(emptyValues) }}</div>
      </div>

      <div class="test-item">
        <label>基础多选 Select（固定宽度测试换行）：</label>
        <sp-select
          v-model:value="selectedValues"
          :options="options"
          placeholder="请选择"
          multiple
          clearable
          style="width: 250px"
        />
        <div class="debug-info">
          选中值: {{ JSON.stringify(selectedValues) }}
        </div>
      </div>

      <div class="test-item">
        <label>更窄的多选 Select（测试换行）：</label>
        <sp-select
          v-model:value="selectedValues2"
          :options="moreOptions"
          placeholder="请选择"
          multiple
          clearable
          style="width: 200px"
        />
        <div class="debug-info">
          选中值: {{ JSON.stringify(selectedValues2) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select as SpSelect } from '../../../packages/ui/src/components/select'
  import { Tag } from '../../../packages/ui/src/components/tag'

  // 测试数据
  const emptyValues = ref([]) // 空状态测试
  const selectedValues = ref(['option1', 'option2', 'option3'])
  const selectedValues2 = ref(['js', 'vue', 'react', 'angular'])

  const testTags = ref([
    { id: 1, label: '标签1' },
    { id: 2, label: '标签2' },
    { id: 3, label: '标签3' },
  ])

  const options = [
    { label: '选项一', value: 'option1' },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
  ]

  const moreOptions = [
    { label: 'JavaScript', value: 'js' },
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Angular', value: 'angular' },
    { label: 'TypeScript', value: 'ts' },
    { label: 'Node.js', value: 'nodejs' },
  ]

  // 事件处理
  const handleTagClose = () => {
    console.log('Tag 关闭')
  }

  const removeTag = (id: number) => {
    console.log('移除 Tag:', id)
    testTags.value = testTags.value.filter(tag => tag.id !== id)
  }

  // 调试信息
  console.log('🔍 组件初始化:', {
    selectedValues: selectedValues.value,
    testTags: testTags.value,
    options,
  })
</script>

<style scoped>
  .simple-tag-test {
    padding: 20px;
    max-width: 600px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .simple-tag-test h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
  }

  .test-section {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .test-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
  }

  .test-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
  }

  .test-item:last-child {
    margin-bottom: 0;
  }

  .test-item label {
    font-weight: 600;
    color: #606266;
    font-size: 14px;
  }

  .tags-container {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .debug-info {
    color: #909399;
    font-size: 12px;
    background: #f5f7fa;
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 8px;
    font-family: monospace;
  }
</style>
