import { createApp } from 'vue'
import SpeedUI from '@speed-ui/ui'
import { i18n } from './i18n'
import { router } from './router'
import App from './App.vue'

import './style.css'
// 导入Speed UI默认主题样式
import '@speed-ui/theme-default/src/index.scss'

const app = createApp(App)

app.use(SpeedUI)
app.use(i18n)
app.use(router)

// 同步路由参数和 i18n 语言
router.afterEach((to) => {
  const locale = to.params.locale as string
  if (locale && locale !== i18n.global.locale.value) {
    i18n.global.locale.value = locale as any
  }
})

app.mount('#app') 