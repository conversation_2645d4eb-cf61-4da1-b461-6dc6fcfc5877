@use '../common/var.scss' as *;

// Dropdown 下拉菜单组件样式
.sp-dropdown {
  position: relative;
  display: inline-block;

  // 触发器区域
  &__trigger {
    cursor: pointer;
    display: inline-block;
    outline: none;

    &:focus-visible {
      outline: 2px solid var(--sp-color-primary);
      outline-offset: 2px;
    }
  }

  // 弹出层
  &__popup {
    position: absolute;
    z-index: 1000;
    min-width: 120px;
    padding: 4px;
    background: white;
    border: 1px solid #{$border-color-base};
    border-radius: #{$border-radius-base};
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform-origin: top left;
  }

  // 内容区域
  &__content {
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    overflow-x: visible; // 确保滚动条不被切割
  }

  // 箭头指示器
  &__arrow {
    position: absolute;
    width: 8px;
    height: 8px;
    background: white;
    border: 1px solid #{$border-color-base};
    transform: rotate(45deg);
    z-index: -1;
  }

  // 状态修饰符
  &--disabled {
    .sp-dropdown__trigger {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  &--visible {
    .sp-dropdown__trigger {
      color: var(--sp-color-primary);
    }
  }

  // 箭头显示状态
  &--arrow {
    .sp-dropdown__popup {
      margin: 4px;
    }
  }

  // 位置修饰符
  &--bottom {
    .sp-dropdown__arrow {
      top: -5px;
      left: 50%;
      transform: translateX(-50%) rotate(45deg);
    }
  }

  &--bottom-start {
    .sp-dropdown__arrow {
      top: -5px;
      left: 12px;
      transform: rotate(45deg);
    }
  }

  &--bottom-end {
    .sp-dropdown__arrow {
      top: -5px;
      right: 12px;
      transform: rotate(45deg);
    }
  }

  &--top {
    .sp-dropdown__arrow {
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%) rotate(45deg);
    }
  }

  &--top-start {
    .sp-dropdown__arrow {
      bottom: -5px;
      left: 12px;
      transform: rotate(45deg);
    }
  }

  &--top-end {
    .sp-dropdown__arrow {
      bottom: -5px;
      right: 12px;
      transform: rotate(45deg);
    }
  }

  &--left {
    .sp-dropdown__arrow {
      right: -5px;
      top: 50%;
      transform: translateY(-50%) rotate(45deg);
    }
  }

  &--left-start {
    .sp-dropdown__arrow {
      right: -5px;
      top: 12px;
      transform: rotate(45deg);
    }
  }

  &--left-end {
    .sp-dropdown__arrow {
      right: -5px;
      bottom: 12px;
      transform: rotate(45deg);
    }
  }

  &--right {
    .sp-dropdown__arrow {
      left: -5px;
      top: 50%;
      transform: translateY(-50%) rotate(45deg);
    }
  }

  &--right-start {
    .sp-dropdown__arrow {
      left: -5px;
      top: 12px;
      transform: rotate(45deg);
    }
  }

  &--right-end {
    .sp-dropdown__arrow {
      left: -5px;
      bottom: 12px;
      transform: rotate(45deg);
    }
  }
}

// 动画过渡效果
.sp-dropdown-enter-active,
.sp-dropdown-leave-active {
  transition: opacity 0.15s ease;
}

.sp-dropdown-enter-from {
  opacity: 0;
}

.sp-dropdown-leave-to {
  opacity: 0;
}

// 深色主题适配
[data-theme='dark'] {
  .sp-dropdown {
    &__popup {
      background: #2c2c2c;
      border-color: #404040;
    }

    &__arrow {
      background: #2c2c2c;
      border-color: #404040;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sp-dropdown {
    &__popup {
      max-width: 90vw;

      .sp-dropdown__content {
        max-height: 50vh;
      }
    }
  }
}
