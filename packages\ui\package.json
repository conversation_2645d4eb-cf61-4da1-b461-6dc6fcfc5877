{"name": "@speed-ui/ui", "version": "1.0.0", "description": "Vue 3 UI 组件库", "main": "lib/index.js", "module": "es/index.js", "types": "types/index.d.ts", "files": ["lib", "es", "types"], "exports": {".": {"types": "./types/index.d.ts", "import": "./es/index.js", "require": "./lib/index.js"}, "./styles": "./lib/style.css", "./package.json": "./package.json"}, "scripts": {"build": "vite build", "clean": "rimraf lib es types", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest run --coverage"}, "peerDependencies": {"vue": "^3.3.0"}, "dependencies": {"@speed-ui/bem-helper": "workspace:*", "@speed-ui/config": "workspace:*", "@speed-ui/hooks": "workspace:*", "@speed-ui/size-helper": "workspace:*", "@speed-ui/utils": "workspace:*", "@vee-validate/i18n": "^4.15.0", "@vee-validate/rules": "^4.15.0", "vee-validate": "^4.15.0"}, "devDependencies": {"@vitejs/plugin-vue-jsx": "^5.0.0"}}