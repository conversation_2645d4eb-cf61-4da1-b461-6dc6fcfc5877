<!--
  FieldContainer.vue - 通用表单字段容器
  提供浮动标签、表单验证、统一样式等功能
  通过插槽支持任何类型的输入元素
-->

<template>
  <div :class="rootClasses">
    <!-- 字段包装器 -->
    <div
      ref="wrapperRef"
      :class="wrapperClasses"
      @click="handleWrapperClick"
    >
      <!-- 浮动标签 -->
      <label
        v-if="label"
        :class="labelClasses"
        :for="fieldId"
        @click="handleLabelClick"
      >
        <span
          v-if="required"
          class="sp-field-container__label-asterisk"
        >
          *
        </span>
        {{ label }}
      </label>

      <!-- 输入元素插槽 -->
      <slot
        :field-id="fieldId"
        :input-classes="inputClasses"
        :input-style="inputStyle"
        :placeholder="computedPlaceholder"
        :disabled="computedDisabled"
        :readonly="readonly"
        :on-focus="handleFocus"
        :on-blur="handleBlur"
        :on-input="handleInput"
        :on-change="handleChange"
        :on-keydown="handleKeydown"
      />

      <!-- 功能区域插槽 -->
      <slot
        name="functions"
        :functions-classes="functionsClasses"
        :icon-size="iconSize"
      />
    </div>

    <!-- 加载动画条 -->
    <div
      v-if="loading"
      :class="loadingBarClasses"
    >
      <div :class="loadingProgressClasses"></div>
    </div>

    <!-- 验证消息 -->
    <transition name="sp-field-container-message">
      <div
        v-if="shouldShowMessage"
        :class="messageClasses"
      >
        {{ currentMessage }}
      </div>
    </transition>

    <!-- 帮助文本 -->
    <div
      v-if="helperText && !shouldShowMessage"
      :class="helperTextClasses"
    >
      {{ helperText }}
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, useId, watch, ref } from 'vue'
  import { useField } from 'vee-validate'
  import { useFieldContainerStyles } from './useFieldContainerStyles'
  import type { FieldContainerProps, FieldContainerEmits } from './types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<FieldContainerProps>(), {
    value: '',
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    error: false,
    loading: false,
    required: false,
    showMessage: true,
    persistentLabel: false,
  })

  const emit = defineEmits<FieldContainerEmits>()

  // ===== 基础状态 =====
  const fieldId = useId()
  const wrapperRef = ref<HTMLDivElement>()
  const isFocused = ref(false)
  const hasValue = ref(false)

  // ===== 表单验证集成 =====
  const veeField = useField(props.name || '', props.rules as any, {
    validateOnValueUpdate: false,
  })

  // ===== 计算属性 =====
  const computedDisabled = computed(() => props.disabled)

  const isLabelFloating = computed(() => {
    return props.persistentLabel || isFocused.value || hasValue.value
  })

  const computedPlaceholder = computed(() => {
    // 当标签浮动时显示 placeholder，否则不显示（避免重复）
    return isLabelFloating.value ? props.placeholder : ''
  })

  const iconSize = computed(() => {
    const sizeMap = {
      small: 16,
      medium: 18,
      large: 20,
    }
    return sizeMap[props.size] || 18
  })

  // ===== 验证状态管理 =====
  const validateState = computed(() => {
    if (props.error) return 'error'
    if (veeField?.meta.valid === false && veeField?.meta.touched) return 'error'
    if (veeField?.meta.valid === true && veeField?.meta.touched) return 'success'
    return ''
  })

  const currentMessage = computed(() => {
    return veeField?.errorMessage.value || ''
  })

  const shouldShowMessage = computed(() => {
    return props.showMessage && !!currentMessage.value
  })

  // ===== 事件处理 =====
  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement | HTMLTextAreaElement
    const value = target.value
    
    // 更新 hasValue 状态
    hasValue.value = !!value
    
    // 发出事件
    emit('update:value', value)
    emit('input', event)
    
    // 同步到 VeeValidate
    veeField?.setValue(value === '' ? undefined : value)
  }

  const handleChange = (event: Event) => {
    emit('change', event)
    // 触发验证
    veeField?.validate()
  }

  const handleFocus = (event: FocusEvent) => {
    isFocused.value = true
    emit('focus', event)
  }

  const handleBlur = async (event: FocusEvent) => {
    isFocused.value = false
    emit('blur', event)
    // 触发验证
    veeField?.handleBlur()
    await veeField?.validate()
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
  }

  const handleWrapperClick = () => {
    // 由具体的输入组件处理聚焦逻辑
    emit('wrapper-click')
  }

  const handleLabelClick = () => {
    if (!computedDisabled.value && !props.readonly) {
      emit('label-click')
    }
  }

  // ===== 同步外部 value 到内部状态 =====
  watch(
    () => props.value,
    (newValue) => {
      hasValue.value = !!newValue
      if (veeField?.value.value !== newValue) {
        veeField?.setValue(newValue)
      }
    },
    { immediate: true }
  )

  // ===== 样式系统 =====
  const {
    rootClasses,
    wrapperClasses,
    labelClasses,
    inputClasses,
    inputStyle,
    functionsClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  } = useFieldContainerStyles(props, {
    computedDisabled,
    isFocused,
    hasValue,
    isLabelFloating,
    validateState,
  })

  // ===== 表单方法 =====
  const validate = async (): Promise<boolean> => {
    const result = await veeField?.validate()
    return result?.valid || false
  }

  const resetField = () => {
    veeField?.resetField()
    emit('update:value', undefined)
    hasValue.value = false
  }

  const clearValidate = () => {
    veeField?.setErrors([])
  }

  // ===== 暴露方法和状态 =====
  defineExpose({
    validate,
    resetField,
    clearValidate,
    get wrapper() {
      return wrapperRef.value || null
    },
    // 暴露状态供子组件使用
    isFocused: computed(() => isFocused.value),
    hasValue: computed(() => hasValue.value),
    isLabelFloating,
    validateState,
  })
</script>

<script lang="ts">
  export default {
    name: 'FieldContainer',
  }
</script>
