<template>
  <div :class="selectClassName">
    <!-- 选择器主体 -->
    <div
      ref="selectRef"
      :class="`${props.prefixCls}__wrapper`"
      @click="handleToggle"
    >
      <!-- 显示区域 -->
      <SelectInput
        :prefixCls="props.prefixCls"
        :multiple="props.multiple"
        :disabled="props.disabled"
        :placeholder="props.placeholder"
        :selectedOption="selectedOption"
        :selectedOptions="selectedOptions"
        :valueKey="props.valueKey"
        :labelKey="props.labelKey"
        :size="props.size"
        @removeTag="handleRemoveTag"
      />

      <!-- 后缀图标 -->
      <div :class="`${props.prefixCls}__suffix`">
        <!-- 清除按钮 -->
        <i
          v-if="clearable && hasValueComputed && !disabled"
          :class="[`${props.prefixCls}__icon`, `${props.prefixCls}__clear`]"
          @click.stop="handleClear"
        >
          <sp-icon
            name="Close"
            :size="16"
          />
        </i>

        <!-- 下拉箭头 -->
        <i
          :class="[
            `${props.prefixCls}__icon`,
            `${props.prefixCls}__arrow`,
            { [`${props.prefixCls}__arrow--reverse`]: isDropdownOpen },
          ]"
        >
          <sp-icon
            name="ChevronDown"
            :size="16"
          />
        </i>
      </div>
    </div>

    <!-- 下拉选项 -->
    <SelectDropdown
      :visible="isDropdownOpen"
      :options="actualOptions"
      :prefixCls="props.prefixCls"
      :dropdownStyle="dropdownStyle"
      :value="modelValue"
      :multiple="props.multiple"
      :valueKey="props.valueKey"
      :labelKey="props.labelKey"
      :emptyText="props.emptyText"
      :variant="props.variant"
      @optionClick="handleSelectOption"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, computed } from 'vue'
  import { classNames } from '../../../packages/ui/src/utils'
  import {
    type SelectProps,
    type SelectEmits,
    type SelectOption,
    selectPropsDefaults,
    getOptionValue,
    isOptionDisabled,
    hasValue,
    getSelectedOption,
    getSelectedOptions,
  } from '../../../packages/ui/src/components/select/select'
  import SelectInput from '../../../packages/ui/src/components/select/internal/SelectInput.vue'
  import SelectDropdown from '../../../packages/ui/src/components/select/internal/SelectDropdown.vue'

  const props = withDefaults(defineProps<SelectProps>(), selectPropsDefaults)
  const emit = defineEmits<SelectEmits>()

  const selectRef = ref<HTMLDivElement>()
  const isDropdownOpen = ref(false)
  const dropdownStyle = ref({})

  // 计算实际使用的选项列表
  const actualOptions = computed(() => {
    return props.options || []
  })

  // 双向绑定
  const modelValue = computed({
    get: () => props.value,
    set: value => {
      emit('update:value', value)
      emit('change', value)
    },
  })

  // 使用工具函数的计算属性
  const hasValueComputed = computed(() =>
    hasValue(modelValue.value, props.multiple)
  )

  const selectedOption = computed(() =>
    getSelectedOption(
      actualOptions.value,
      modelValue.value,
      props.multiple,
      props.valueKey
    )
  )

  const selectedOptions = computed(() =>
    getSelectedOptions(
      actualOptions.value,
      modelValue.value,
      props.multiple,
      props.valueKey
    )
  )

  const selectClassName = computed(() =>
    classNames(props.prefixCls, `${props.prefixCls}--${props.size}`, {
      [`${props.prefixCls}--disabled`]: props.disabled,
      [`${props.prefixCls}--multiple`]: props.multiple,
      [`${props.prefixCls}--clearable`]: props.clearable && hasValueComputed.value,
    })
  )

  // 切换下拉框显示
  const handleToggle = () => {
    if (props.disabled) return
    isDropdownOpen.value = !isDropdownOpen.value
  }

  // 选择选项
  const handleSelectOption = (option: SelectOption) => {
    if (isOptionDisabled(option)) return

    const value = getOptionValue(option, props.valueKey)

    if (props.multiple) {
      const values = Array.isArray(modelValue.value) ? [...modelValue.value] : []
      const index = values.indexOf(value)

      if (index > -1) {
        values.splice(index, 1)
      } else {
        values.push(value)
      }

      modelValue.value = values
    } else {
      modelValue.value = value
      isDropdownOpen.value = false
    }
  }

  // 移除标签（多选）
  const handleRemoveTag = (option: SelectOption) => {
    if (props.disabled) return

    const value = getOptionValue(option, props.valueKey)
    const values = Array.isArray(modelValue.value) ? [...modelValue.value] : []
    const index = values.indexOf(value)

    if (index > -1) {
      values.splice(index, 1)
      modelValue.value = values
    }
  }

  // 清除
  const handleClear = () => {
    if (props.disabled) return
    modelValue.value = props.multiple ? [] : undefined
    emit('clear')
    isDropdownOpen.value = false
  }
</script>

<script lang="ts">
  export default {
    name: 'TestSelect',
  }
</script>
