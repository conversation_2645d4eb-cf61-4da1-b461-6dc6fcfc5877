<!-- 
  这是一个示例文件，展示如何在 Textarea 组件中复用 InputCore 的 props
-->
<template>
  <div class="sp-textarea">
    <InputCore
      v-bind="inputCoreProps"
      mode="input"
      @input="handleInput"
      @change="handleChange"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <!-- 使用 inner 插槽来渲染 textarea 元素 -->
      <template #inner>
        <textarea
          ref="textareaRef"
          :value="props.modelValue"
          :placeholder="props.placeholder"
          :disabled="props.disabled"
          :readonly="props.readonly"
          :maxlength="props.maxlength"
          :rows="props.rows"
          :cols="props.cols"
          class="sp-textarea__element"
          @input="handleTextareaInput"
          @change="handleTextareaChange"
          @focus="handleTextareaFocus"
          @blur="handleTextareaBlur"
        />
      </template>
    </InputCore>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref } from 'vue'
import InputCore from '../input/inputcore'
import { makeInputCoreProps } from '../input/inputCoreProps'

// 扩展 InputCore 的 props，添加 Textarea 特有的属性
const makeTextareaProps = () => {
  return {
    // 继承 InputCore 的大部分 props（排除一些不适用的）
    ...makeInputCoreProps(),
    
    // Textarea 特有的 props
    modelValue: {
      type: String,
      default: '',
    },
    rows: {
      type: Number,
      default: 3,
    },
    cols: {
      type: Number,
      default: undefined,
    },
    autosize: {
      type: Boolean,
      default: false,
    },
    resize: {
      type: String as () => 'none' | 'both' | 'horizontal' | 'vertical',
      default: 'vertical',
    },
  }
}

// 定义 props，可以覆盖默认值
const props = defineProps({
  ...makeTextareaProps(),
  // Textarea 特有的默认值覆盖
  showWordLimit: {
    type: Boolean,
    default: true,  // Textarea 默认显示字数统计
  },
  clearable: {
    type: Boolean,
    default: true,  // Textarea 默认可清除
  },
  // 排除不适用于 textarea 的 type 属性
  type: {
    type: String,
    default: 'text',
    validator: () => true, // 忽略验证，因为 textarea 不需要 type
  },
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: string]
  input: [event: Event]
  change: [event: Event]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

// 模板引用
const textareaRef = ref<HTMLTextAreaElement>()

// 计算传递给 InputCore 的 props
const inputCoreProps = computed(() => {
  const { 
    modelValue, 
    rows, 
    cols, 
    autosize, 
    resize, 
    type,  // 排除 type，因为 textarea 不需要
    ...coreProps 
  } = props
  
  return {
    ...coreProps,
    value: modelValue,
    hasValue: Boolean(modelValue),
  }
})

// 事件处理
const handleTextareaInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  emit('update:modelValue', target.value)
}

const handleTextareaChange = (event: Event) => {
  // 可以在这里添加 textarea 特有的逻辑
}

const handleTextareaFocus = (event: FocusEvent) => {
  // 可以在这里添加 textarea 特有的逻辑
}

const handleTextareaBlur = (event: FocusEvent) => {
  // 可以在这里添加 textarea 特有的逻辑
}

// 传递给父组件的事件处理
const handleInput = (event: Event) => {
  emit('input', event)
}

const handleChange = (event: Event) => {
  emit('change', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

// 暴露方法给父组件
defineExpose({
  textareaRef,
  focus: () => textareaRef.value?.focus(),
  blur: () => textareaRef.value?.blur(),
})
</script>

<style scoped>
.sp-textarea {
  /* Textarea 组件特有的样式 */
}

.sp-textarea__element {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  resize: v-bind(resize);
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
</style>
