<text>
> 可以通过各种颜色属性自定义开关的外观
</text>

<template>
    <sp-switch 
        v-model:value="value1" 
        activeColor="#13ce66" 
        inactiveColor="#ff4949" 
    />
    <sp-switch 
        v-model:value="value2"
        switchOnColor="#409eff"
        switchOffColor="#dcdfe6"
    />
    <sp-switch
        v-model:value="value3"
        buttonOnColor="#67c23a"
        buttonOffColor="#f56c6c"
    />
    <sp-switch 
        v-model:value="value4" 
        switchColor="#e6a23c" 
        buttonColor="#ffffff" 
    />
</template>

<script setup>
import { ref } from 'vue'

const value1 = ref(true)
const value2 = ref(false)
const value3 = ref(true)
const value4 = ref(false)
</script>