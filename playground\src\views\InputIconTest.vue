<template>
  <div class="input-icon-test">
    <h1>Input 图标占位测试</h1>

    <div class="test-section">
      <h2>1. 原始问题演示</h2>
      <p>使用内联样式处理图标占位（问题方案）：</p>

      <!-- 使用内联样式的输入框 -->
      <div class="demo-item">
        <label>用户名（内联样式）：</label>
        <div class="old-input-wrapper">
          <span class="old-prefix">👤</span>
          <input
            class="old-input"
            placeholder="请输入用户名"
            :style="{ paddingLeft: '40px' }"
          />
        </div>
      </div>

      <div class="demo-item">
        <label>密码（内联样式）：</label>
        <div class="old-input-wrapper">
          <input
            class="old-input"
            type="password"
            placeholder="请输入密码"
            :style="{ paddingRight: '40px' }"
          />
          <span class="old-suffix">👁️</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>2. 改进方案演示</h2>
      <p>使用 Flexbox 布局和 CSS 类名（推荐方案）：</p>

      <!-- 使用新的wrapper布局 -->
      <div class="demo-item">
        <label>用户名（Flexbox）：</label>
        <div class="sp-input__wrapper">
          <span class="sp-input__prefix">👤</span>
          <input
            class="sp-input__inner"
            placeholder="请输入用户名"
          />
        </div>
      </div>

      <div class="demo-item">
        <label>密码（Flexbox）：</label>
        <div class="sp-input__wrapper">
          <input
            class="sp-input__inner"
            type="password"
            placeholder="请输入密码"
          />
          <span class="sp-input__suffix">👁️</span>
        </div>
      </div>

      <div class="demo-item">
        <label>搜索（前后缀）：</label>
        <div class="sp-input__wrapper">
          <span class="sp-input__prefix">🔍</span>
          <input
            class="sp-input__inner"
            placeholder="搜索内容"
          />
          <span class="sp-input__suffix">✕</span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 实际组件测试</h2>
      <p>使用 Speed UI Input 组件：</p>

      <div class="demo-item">
        <label>用户名：</label>
        <Input
          v-model:value="username"
          placeholder="请输入用户名"
          prefix-icon="user"
        />
      </div>

      <div class="demo-item">
        <label>密码：</label>
        <Input
          v-model:value="password"
          type="password"
          placeholder="请输入密码"
          suffix-icon="eye"
          show-password
        />
      </div>

      <div class="demo-item">
        <label>搜索：</label>
        <Input
          v-model:value="search"
          placeholder="搜索内容"
          prefix-icon="search"
          clearable
        />
      </div>
    </div>

    <div class="test-section">
      <h2>4. 对比说明</h2>
      <div class="comparison">
        <div class="comparison-item">
          <h3>❌ 内联样式方案问题</h3>
          <ul>
            <li>需要动态计算 padding 值</li>
            <li>图标大小变化时需要重新计算</li>
            <li>代码不够优雅，维护困难</li>
            <li>无法利用 CSS 的强大功能</li>
          </ul>
        </div>

        <div class="comparison-item">
          <h3>✅ Flexbox 方案优势</h3>
          <ul>
            <li>使用 CSS 类名，样式统一管理</li>
            <li>Flexbox 自动处理空间分配</li>
            <li>图标和输入框自然对齐</li>
            <li>易于维护和扩展</li>
            <li>符合现代前端最佳实践</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Input } from '@speed-ui/ui'

  const username = ref('')
  const password = ref('')
  const search = ref('')
</script>

<style scoped>
  .input-icon-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;
  }

  .demo-item {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .demo-item label {
    width: 120px;
    font-weight: 500;
  }

  /* 旧的内联样式方案样式 */
  .old-input-wrapper {
    position: relative;
    display: inline-block;
    width: 200px;
  }

  .old-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d0d0d0;
    border-radius: 4px;
    font-size: 14px;
  }

  .old-prefix,
  .old-suffix {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    z-index: 1;
  }

  .old-prefix {
    left: 8px;
  }

  .old-suffix {
    right: 8px;
  }

  /* 新的 Flexbox 方案样式 */
  /* .sp-input__wrapper {
  display: flex;
  align-items: center;
  width: 200px;
  padding: 8px 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  background-color: white;
  transition: border-color 0.2s;
} */

  .sp-input__wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  }

  .sp-input__inner {
    border: none;
    padding: 0;
    background: transparent;
    flex: 1;
    min-width: 0;
    font-size: 14px;
    outline: none;
  }

  .sp-input__prefix {
    margin-right: 8px;
    color: #999;
    flex-shrink: 0;
  }

  .sp-input__suffix {
    margin-left: 8px;
    color: #999;
    flex-shrink: 0;
    cursor: pointer;
  }

  .sp-input__suffix:hover {
    color: #667eea;
  }

  .comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 20px;
  }

  .comparison-item {
    padding: 16px;
    border-radius: 6px;
  }

  .comparison-item:first-child {
    background: #fff5f5;
    border: 1px solid #fed7d7;
  }

  .comparison-item:last-child {
    background: #f0fff4;
    border: 1px solid #c6f6d5;
  }

  .comparison-item h3 {
    margin: 0 0 12px 0;
    font-size: 16px;
  }

  .comparison-item ul {
    margin: 0;
    padding-left: 20px;
  }

  .comparison-item li {
    margin-bottom: 8px;
    line-height: 1.5;
  }
</style>
