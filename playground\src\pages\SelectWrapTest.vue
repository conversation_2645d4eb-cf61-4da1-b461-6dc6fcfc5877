<template>
  <div class="select-wrap-test">
    <h1>Select 多选换行测试</h1>
    <p>测试多选标签换行时输入框高度自适应</p>

    <div class="test-container">
      <div class="test-section">
        <h3>🔥 多选换行测试</h3>
        
        <div class="test-item">
          <label>测试1: 标准宽度 - 观察换行效果</label>
          <Select
            v-model:value="testValue1"
            :options="longOptions"
            placeholder="选择多个选项观察换行"
            multiple
            clearable
            style="width: 300px"
          />
          <div class="test-result">
            <strong>选中数量:</strong> {{ testValue1.length }} 个
          </div>
        </div>

        <div class="test-item">
          <label>测试2: 窄宽度 - 强制换行</label>
          <Select
            v-model:value="testValue2"
            :options="longOptions"
            placeholder="窄宽度测试"
            multiple
            clearable
            style="width: 200px"
          />
          <div class="test-result">
            <strong>选中数量:</strong> {{ testValue2.length }} 个
          </div>
        </div>

        <div class="test-item">
          <label>测试3: 不同尺寸的换行效果</label>
          <div style="display: flex; flex-direction: column; gap: 15px;">
            <div>
              <label style="font-size: 12px; margin-bottom: 5px;">Small 尺寸:</label>
              <Select
                v-model:value="testValue3"
                :options="longOptions"
                placeholder="小尺寸多选"
                multiple
                size="small"
                style="width: 250px"
              />
            </div>
            <div>
              <label style="font-size: 12px; margin-bottom: 5px;">Medium 尺寸:</label>
              <Select
                v-model:value="testValue4"
                :options="longOptions"
                placeholder="中等尺寸多选"
                multiple
                size="medium"
                style="width: 250px"
              />
            </div>
            <div>
              <label style="font-size: 12px; margin-bottom: 5px;">Large 尺寸:</label>
              <Select
                v-model:value="testValue5"
                :options="longOptions"
                placeholder="大尺寸多选"
                multiple
                size="large"
                style="width: 250px"
              />
            </div>
          </div>
        </div>

        <div class="test-item">
          <label>测试4: 预设多个选项</label>
          <Select
            v-model:value="testValue6"
            :options="longOptions"
            placeholder="已有多个选项"
            multiple
            clearable
            style="width: 280px"
          />
          <div class="test-result">
            <strong>选中数量:</strong> {{ testValue6.length }} 个
          </div>
        </div>
      </div>

      <div class="test-section">
        <h3>📏 极限测试</h3>
        
        <div class="test-item">
          <label>测试5: 超多选项</label>
          <Select
            v-model:value="testValue7"
            :options="manyOptions"
            placeholder="选择很多选项"
            multiple
            clearable
            style="width: 350px"
          />
          <div class="test-result">
            <strong>选中数量:</strong> {{ testValue7.length }} 个
          </div>
        </div>

        <div class="test-item">
          <label>测试6: 超窄容器</label>
          <Select
            v-model:value="testValue8"
            :options="longOptions"
            placeholder="超窄"
            multiple
            clearable
            style="width: 150px"
          />
        </div>
      </div>
    </div>

    <div class="test-summary">
      <h3>📊 测试要点</h3>
      <ul>
        <li>✅ 输入框高度应该随标签换行自动增长</li>
        <li>✅ 标签不应该溢出输入框边界</li>
        <li>✅ 不同尺寸的输入框都应该正确换行</li>
        <li>✅ 前缀和后缀图标应该保持正确位置</li>
        <li>✅ 清除按钮应该始终可见和可用</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import Select from '../../../packages/ui/src/components/select/select.vue'

  // 测试数据
  const testValue1 = ref([])
  const testValue2 = ref([])
  const testValue3 = ref([])
  const testValue4 = ref([])
  const testValue5 = ref([])
  const testValue6 = ref(['option1', 'option2', 'option3', 'option4']) // 预设值
  const testValue7 = ref([])
  const testValue8 = ref([])

  // 长选项名称
  const longOptions = [
    { label: '这是一个很长的选项名称1', value: 'option1' },
    { label: '选项2', value: 'option2' },
    { label: '这是另一个长选项3', value: 'option3' },
    { label: '短选项4', value: 'option4' },
    { label: '超级长的选项名称5', value: 'option5' },
    { label: '选项6', value: 'option6' },
    { label: '中等长度的选项7', value: 'option7' },
    { label: '选项8', value: 'option8' },
  ]

  // 更多选项
  const manyOptions = [
    { label: 'JavaScript', value: 'js' },
    { label: 'TypeScript', value: 'ts' },
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Angular', value: 'angular' },
    { label: 'Node.js', value: 'nodejs' },
    { label: 'Python', value: 'python' },
    { label: 'Java', value: 'java' },
    { label: 'C++', value: 'cpp' },
    { label: 'Go', value: 'go' },
    { label: 'Rust', value: 'rust' },
    { label: 'PHP', value: 'php' },
    { label: 'Ruby', value: 'ruby' },
    { label: 'Swift', value: 'swift' },
    { label: 'Kotlin', value: 'kotlin' },
  ]
</script>

<style scoped>
  .select-wrap-test {
    padding: 20px;
    max-width: 1000px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  .select-wrap-test h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 10px;
  }

  .select-wrap-test p {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
  }

  .test-container {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .test-section {
    margin-bottom: 40px;
  }

  .test-section:last-child {
    margin-bottom: 0;
  }

  .test-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
    font-size: 18px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
  }

  .test-item {
    margin-bottom: 25px;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fafafa;
  }

  .test-item label {
    display: block;
    font-weight: 600;
    color: #606266;
    margin-bottom: 10px;
    font-size: 14px;
  }

  .test-result {
    margin-top: 10px;
    padding: 8px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
    color: #606266;
  }

  .test-summary {
    margin-top: 30px;
    padding: 20px;
    background: #f0f9ff;
    border-radius: 8px;
    border: 1px solid #bfdbfe;
  }

  .test-summary h3 {
    margin-top: 0;
    color: #1e40af;
  }

  .test-summary ul {
    margin: 0;
    padding-left: 20px;
  }

  .test-summary li {
    margin-bottom: 8px;
    color: #374151;
    line-height: 1.5;
  }
</style>
