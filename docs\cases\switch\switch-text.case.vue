<text>
> 可以通过 `onText` 和 `offText` 设置开关内部显示的文本，通过 `onOutText` 和 `offOutText` 设置开关外部显示的文本
</text>

<template>
    <div class="button-demo-row">
        <sp-switch v-model:value="value1" onText="开" offText="关" />
        <sp-switch v-model:value="value2" onText="ON" offText="OFF" />
        <sp-switch v-model:value="value3" onOutText="已开启" offOutText="已关闭" />
        <sp-switch v-model:value="value4" onText="是" offText="否" onOutText="启用" offOutText="禁用" />
        <sp-switch v-model:value="value4" onText="一个很长的文本" offText="短文本" />
    </div>
</template>

<script setup>
import { ref } from 'vue'

const value1 = ref(false)
const value2 = ref(true)
const value3 = ref(false)
const value4 = ref(true)
</script>

<style scoped>
.switch-demo-row {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
}
</style>