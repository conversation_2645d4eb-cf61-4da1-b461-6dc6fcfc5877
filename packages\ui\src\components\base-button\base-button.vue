<template>
  <button
    :disabled="disabled || loading || isCountingDown"
    type="button"
    @click="handleClick"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseLeave"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel"
    v-bind="$attrs"
  >
    <!-- tip 插槽 -->
    <slot
      name="tip"
      v-if="tipConfig"
      :tipConfig="tipConfig"
    >
      <span
        :style="{
          background: tipConfig.bgColor,
          color: tipConfig.color,
          top: tipConfig.top,
          right: tipConfig.right,
          fontSize: tipConfig.fontSize,
          padding: tipConfig.padding,
        }"
      >
        {{ tipConfig.text }}
      </span>
    </slot>

    <!-- loading 插槽 -->
    <slot
      name="loading"
      v-if="loading"
    >
      <span>
        <component
          v-if="loadingIcon"
          :is="loadingIcon"
          style="width: 1em; height: 1em"
        />
        <span v-else></span>
      </span>
    </slot>

    <!-- countdown 插槽 -->
    <slot
      name="countdown"
      v-if="isCountingDown"
      :countdownTime="countdownTime"
    >
      <span>{{ countdownTime }}s</span>
    </slot>

    <!-- 长按进度条插槽 -->
    <slot
      name="press-progress"
      v-if="pressDown"
      :isPressing="isPressing"
    >
      <div :class="{ 'press-progress-active': isPressing }"></div>
    </slot>

    <!-- 内容插槽 -->
    <slot name="content">
      <span :class="{ 'content-vertical': vertical }">
        <component
          v-if="icon"
          :is="icon"
        />
        <span v-if="vertical">
          <slot />
        </span>
        <template v-else>
          <slot />
        </template>
      </span>
    </slot>
  </button>
</template>

<script setup lang="ts">
  import { computed, ref, onUnmounted } from 'vue'
  import type { ButtonProps, ButtonEmits, TipConfig } from '../button/button'

  // 使用完整的 ButtonProps 接口
  const props = withDefaults(defineProps<ButtonProps>(), {
    primary: false,
    secondary: false,
    dashed: false,
    text: false,
    link: false,
    disabled: false,
    loading: false,
    loadingIcon: undefined,
    toggleable: false,
    attrStyle: () => ({}),
    size: 'medium',
    circle: false,
    type: undefined,
    icon: undefined,
    time: undefined,
    vertical: false,
    round: false,
    square: false,
    pressDown: undefined,
    tip: '',
  })

  const emit = defineEmits<ButtonEmits>()

  // 倒计时相关
  const isCountingDown = ref(false)
  const countdownTime = ref(0)
  let countdownTimer: number | null = null

  // 长按相关
  const isPressing = ref(false)
  let pressTimer: number | null = null

  // 状态管理
  const isActive = ref(false)

  // 按钮状态计算属性
  const buttonState = computed(() => ({
    isActive: isActive.value,
    isPressing: isPressing.value,
    isCountingDown: isCountingDown.value,
  }))

  // 倒计时功能
  const startCountdown = () => {
    if (!props.time) return
    isCountingDown.value = true
    countdownTime.value = props.time
    countdownTimer = window.setInterval(() => {
      countdownTime.value--
      emit('countdown', countdownTime.value)
      if (countdownTime.value <= 0) {
        stopCountdown()
      }
    }, 1000)
  }

  const stopCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
    isCountingDown.value = false
    countdownTime.value = 0
  }

  // 长按功能
  const startPressing = (event: MouseEvent | TouchEvent) => {
    if (
      !props.pressDown ||
      props.disabled ||
      props.loading ||
      isCountingDown.value
    )
      return

    isPressing.value = true
    emit('pressStart', event as MouseEvent)

    // 设置定时器，在指定时间后触发完成事件
    pressTimer = window.setTimeout(() => {
      if (isPressing.value) {
        emit('pressComplete', event as MouseEvent)
        stopPressing()
      }
    }, props.pressDown)
  }

  const stopPressing = () => {
    if (pressTimer) {
      clearTimeout(pressTimer)
      pressTimer = null
    }
    if (isPressing.value) {
      emit('pressEnd', new MouseEvent('mouseup'))
    }
    isPressing.value = false
  }

  // 点击处理
  const handleClick = (event: MouseEvent) => {
    if (!props.disabled && !props.loading && !isCountingDown.value) {
      // 如果有长按功能且正在长按，则不触发点击
      if (props.pressDown && isPressing.value) {
        return
      }

      if (props.toggleable) {
        isActive.value = !isActive.value
        emit('toggle', isActive.value)
      }
      emit('click', event)
      if (props.time) {
        startCountdown()
      }
    }
  }

  // 鼠标事件处理
  const handleMouseDown = (event: MouseEvent) => {
    if (props.pressDown) {
      startPressing(event)
    }
  }

  const handleMouseUp = () => {
    if (props.pressDown) {
      stopPressing()
    }
  }

  const handleMouseLeave = () => {
    if (props.pressDown) {
      stopPressing()
    }
  }

  // 触摸事件处理
  const handleTouchStart = (event: TouchEvent) => {
    if (props.pressDown) {
      event.preventDefault()
      startPressing(event)
    }
  }

  const handleTouchEnd = (event: TouchEvent) => {
    if (props.pressDown) {
      event.preventDefault()
      stopPressing()
    }
  }

  const handleTouchCancel = (event: TouchEvent) => {
    if (props.pressDown) {
      event.preventDefault()
      stopPressing()
    }
  }

  // tip 解析
  const tipConfig = computed(() => {
    if (!props.tip) return null
    if (typeof props.tip === 'string') {
      return {
        text: props.tip,
        bgColor: '#a7a2a2',
        color: '#fff',
        top: '-12px',
        right: '-8px',
        fontSize: '10px',
        padding: '1.5px 5px',
      }
    }
    return {
      bgColor: '#a7a2a2',
      color: '#fff',
      top: '-12px',
      right: '-8px',
      fontSize: '10px',
      padding: '1.5px 5px',
      ...props.tip,
      text: props.tip.text || '',
    }
  })

  onUnmounted(() => {
    stopCountdown()
    stopPressing()
  })

  // 暴露状态和方法给父组件
  defineExpose({
    // 状态
    isActive: computed(() => isActive.value),
    isPressing: computed(() => isPressing.value),
    isCountingDown: computed(() => isCountingDown.value),
    disabled: computed(() => props.disabled),
    loading: computed(() => props.loading),
    buttonState,

    // 方法
    startCountdown,
    stopCountdown,
    startPressing,
    stopPressing,
  })
</script>

<style>
  /* 
  Base Button 只包含最基础的重置样式
  确保跨浏览器一致性，不包含任何视觉样式
*/

  /* 重置默认样式 */
  button {
    border: none;
    background: none;
    padding: 0;
    margin: 0;
    font: inherit;
    color: inherit;
    cursor: pointer;
    outline: none;
    text-decoration: none;
    display: inline-block;
    box-sizing: border-box;
  }

  /* 禁用状态 */
  button:disabled,
  [aria-disabled='true'] {
    cursor: not-allowed;
    pointer-events: none;
  }

  /* 移除默认的焦点样式，让上层组件处理 */
  button:focus,
  button:focus-visible {
    outline: none;
  }

  /* 确保按钮内容不被选中 */
  button {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }

  /* 移除 iOS Safari 的默认样式 */
  button {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }

  /* 确保 flex 布局正常工作 */
  button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    position: relative;
  }

  /* 长按进度条基础样式 */
  .press-progress-active {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0.6) 100%
    );
    z-index: 0;
    border-radius: inherit;
    transition: none;
    overflow: hidden;
    animation: pressProgress var(--press-duration, 1000ms) linear forwards;
  }

  .content-vertical {
    flex-direction: column;
  }

  @keyframes pressProgress {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }
</style>
