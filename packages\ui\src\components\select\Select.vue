<!--
  Select.vue - 使用 sp-input 作为基底的选择器组件
  参考 select-field.vue 使用 input-field.vue 的模式
-->

<template>
  <div
    :class="['select-wrapper', { 'select-wrapper--multiple': props.multiple }]"
    @click="handleClick"
  >
    <!-- 直接使用 sp-input，享受所有功能 -->
    <sp-input
      ref="inputRef"
      v-bind="inputProps"
      :value="displayValue"
      @clear="handleClear"
      @focus="handleInputFocus"
      @blur="handleInputBlur"
      @input="baseEventHandlers.handleInput"
      @change="baseEventHandlers.handleChange"
      @keydown="baseEventHandlers.handleKeydown"
      @prefix-click="baseEventHandlers.handlePrefixClick"
      @suffix-click="baseEventHandlers.handleSuffixClick"
    >
      <!-- 内部插槽：多选标签 - 完全替换输入框内容 -->
      <template
        #inner
        v-if="props.multiple"
      >
        <div
          class="sp-select__inner-content"
          tabindex="0"
          @focus="handleMultiSelectFocus"
          @blur="handleMultiSelectBlur"
        >
          <!-- 多选标签 -->
          <div
            v-if="selectedOptions.length > 0"
            class="sp-select__tags"
          >
            <Tag
              v-for="option in selectedOptions"
              :key="getOptionValue(option)"
              :label="getOptionLabel(option)"
              :closable="!props.disabled"
              :size="getTagSize()"
              type="default"
              variant="light"
              class="sp-select__tag"
              @close="handleRemoveTag(option)"
            />
          </div>

          <!-- 占位符 -->
          <span
            v-if="selectedOptions.length === 0"
            class="sp-select__placeholder"
          >
            {{ props.placeholder }}
          </span>
        </div>
      </template>

      <!-- 后缀：下拉箭头 -->
      <template #suffix>
        <sp-icon
          name="ChevronDown"
          :size="16"
          :class="arrowClasses"
        />
      </template>
    </sp-input>

    <!-- 下拉选项面板 - 复用现有的 SelectDropdown 组件 -->
    <SelectDropdown
      ref="dropdownRef"
      :visible="isDropdownOpen"
      :options="props.options || []"
      prefix-cls="sp-select"
      :dropdown-style="dropdownStyle"
      :value="props.value"
      :multiple="props.multiple"
      :value-key="props.valueKey || 'value'"
      :label-key="props.labelKey || 'label'"
      :empty-text="props.emptyText || '暂无数据'"
      :size="props.size"
      @optionClick="handleSelectOption"
      @mousedown.prevent.stop
    />
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    computed,
    nextTick,
    onMounted,
    onUnmounted,
    defineExpose,
    watch,
  } from 'vue'
  import SpIcon from '../icon/Icon.vue'
  import SelectDropdown from './internal/SelectDropdown.vue'
  import { Tag } from '../tag'
  import type { SelectProps, SelectEmits } from './select'
  import { selectPropsDefaults } from './select'
  import type { SelectOption } from './select'
  import { createInputEventHandlers } from '../../utils/eventFactory'
  import './style'

  /**
   * Select 组件 - 使用 sp-input 作为基底
   *
   * 简单的实现：
   * 1. 直接使用 sp-input 组件
   * 2. 添加下拉选项功能
   * 3. 享受所有现有特性
   */

  const props = withDefaults(defineProps<SelectProps>(), selectPropsDefaults)
  const emit = defineEmits<SelectEmits>()

  // 组件引用
  const inputRef = ref<any>(null)
  const dropdownRef = ref<InstanceType<typeof SelectDropdown> | null>(null)

  // 状态
  const isDropdownOpen = ref(false)
  const dropdownStyle = ref({})
  const isSelecting = ref(false) // 添加选择状态标记
  const isFocused = ref(false) // 手动管理焦点状态

  // 计算属性
  const selectedValues = computed(() => {
    if (props.multiple) {
      return Array.isArray(props.value) ? props.value : []
    }
    return props.value !== undefined ? [props.value] : []
  })

  const selectedOptions = computed(() => {
    if (!props.options) return []
    return selectedValues.value
      .map((value: any) => {
        return props.options?.find(opt => getOptionValue(opt) === value)
      })
      .filter(Boolean) as SelectOption[]
  })

  const displayValue = computed(() => {
    if (props.multiple) {
      // 多选模式下不显示文本，使用标签显示
      return ''
    } else {
      // 单选模式下正常显示选中的文本
      const option = props.options?.find(
        (opt: SelectOption) => getOptionValue(opt) === props.value
      )
      return option ? getOptionLabel(option) : ''
    }
  })

  const arrowClasses = computed(() => {
    const classes = ['select-arrow']
    if (isDropdownOpen.value) {
      classes.push('select-arrow--reverse')
    }
    return classes.join(' ')
  })

  // 计算是否有值（用于清除按钮显示）
  const hasValue = computed(() => {
    if (props.multiple) {
      return selectedValues.value.length > 0
    } else {
      return (
        props.value !== undefined && props.value !== null && props.value !== ''
      )
    }
  })

  // 传递给 sp-input 的核心属性
  const inputProps = computed(() => ({
    placeholder: props.placeholder,
    disabled: props.disabled,
    readonly: false, // Select 始终只读
    size: props.size,
    clearable: props.clearable,
    variant: props.variant,
    error: props.validateState === 'error',
    validateState: props.validateState,
    validateMessage: props.validateMessage,
    hasValue: hasValue.value,
    focused: isFocused.value,
    loading: props.loading,
    prefixIcon: props.prefixIcon,
    effect: props.effect,
    mode: props.multiple ? 'display' : undefined, // 多选模式下使用display模式，不渲染input元素
  }))

  // 工具函数
  const getOptionValue = (option: SelectOption) => {
    return option[props.valueKey || 'value']
  }

  const getOptionLabel = (option: SelectOption) => {
    return option[props.labelKey || 'label']
  }

  // 根据 Select 尺寸获取 Tag 尺寸
  const getTagSize = () => {
    const sizeMap = {
      small: 'small',
      medium: 'medium',
      large: 'large',
    }
    return sizeMap[props.size || 'medium'] as 'small' | 'medium' | 'large'
  }

  // 处理移除标签
  const handleRemoveTag = (option: SelectOption) => {
    if (props.disabled) return

    const optionValue = getOptionValue(option)
    const currentValues = Array.isArray(props.value) ? props.value : []
    const newValue = currentValues.filter(
      (v: string | number) => v !== optionValue
    )

    emit('update:value', newValue)
    emit('change', newValue)
  }

  // 使用事件工厂创建基础事件处理器
  const baseEventHandlers = createInputEventHandlers(emit, inputRef, props)

  // 简化的点击处理
  const handleClick = (event: MouseEvent) => {
    if (props.disabled) return

    emit('click', event)
    isDropdownOpen.value = !isDropdownOpen.value

    if (isDropdownOpen.value) {
      isFocused.value = true
      emit('focus')
      nextTick(() => {
        updateDropdownPosition()
        inputRef.value?.focus?.()
      })
    } else if (!isSelecting.value) {
      isFocused.value = false
      emit('blur')
    }
  }

  const handleSelectOption = (option: SelectOption) => {
    if (option.disabled) return

    isSelecting.value = true
    const optionValue = getOptionValue(option)
    let newValue: string | number | Array<string | number> | undefined

    if (props.multiple) {
      // 多选：切换选项
      const currentValues = Array.isArray(props.value) ? props.value : []
      const index = currentValues.indexOf(optionValue)
      newValue =
        index > -1
          ? currentValues.filter(v => v !== optionValue)
          : [...currentValues, optionValue]
    } else {
      // 单选：直接设置值并关闭下拉框
      newValue = optionValue
      isDropdownOpen.value = false
    }

    emit('update:value', newValue)
    emit('change', newValue)

    // 重置选择状态并恢复焦点
    nextTick(() => {
      isSelecting.value = false
      inputRef.value?.focus?.()
    })
  }

  // 简化的清除处理
  const handleClear = () => {
    if (props.disabled) return

    const newValue = props.multiple ? [] : undefined
    emit('update:value', newValue)
    emit('clear')
  }

  // 简化的焦点事件处理
  const handleInputFocus = () => {
    if (!isFocused.value) {
      isFocused.value = true
      emit('focus')
    }
  }

  const handleInputBlur = () => {
    if (!isDropdownOpen.value && !isSelecting.value) {
      isFocused.value = false
      emit('blur')
    }
  }

  const handleMultiSelectFocus = handleInputFocus
  const handleMultiSelectBlur = handleInputBlur

  // 下拉框位置
  const updateDropdownPosition = () => {
    if (!inputRef.value) return

    const rect = inputRef.value.$el.getBoundingClientRect()

    // 根据不同变体调整下拉框位置
    let topOffset = 4

    // 对于下划线和填充变体，下拉框应该紧贴底部边框
    if (props.variant === 'underlined' || props.variant === 'filled') {
      topOffset = 0 // 紧贴底部边框
    }

    dropdownStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + topOffset}px`,
      left: `${rect.left}px`,
      width: `${rect.width}px`,
      zIndex: 9999,
      // 禁用动画，避免飘动效果
      transition: 'none',
      transform: 'none',
    }
  }

  // 简化的外部点击处理
  const handleClickOutside = (event: MouseEvent) => {
    if (isSelecting.value || !isDropdownOpen.value) return

    const target = event.target as Node
    const dropdownEl = dropdownRef.value?.$el
    const inputEl = inputRef.value?.$el

    // 如果点击的是下拉框或输入框内部，不处理
    if (
      (dropdownEl && dropdownEl.contains(target)) ||
      (inputEl && inputEl.contains(target))
    ) {
      return
    }

    // 关闭下拉框并失焦
    isDropdownOpen.value = false
    isFocused.value = false
    emit('blur')
  }

  // 处理窗口滚动和调整大小
  const handleResize = () => {
    if (isDropdownOpen.value) {
      updateDropdownPosition()
    }
  }

  // 统一的位置更新函数
  const updatePositionIfNeeded = () => {
    if (isDropdownOpen.value) {
      nextTick(() => updateDropdownPosition())
    }
  }

  // 监听需要更新位置的变化
  watch(
    [() => selectedValues.value, () => isDropdownOpen.value],
    updatePositionIfNeeded,
    { deep: true }
  )

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
    window.addEventListener('scroll', handleResize, true)
    window.addEventListener('resize', handleResize)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
    window.removeEventListener('scroll', handleResize, true)
    window.removeEventListener('resize', handleResize)
  })

  // 暴露方法
  const focus = () => inputRef.value?.focus?.()
  const blur = () => inputRef.value?.blur?.()
  const clear = () => handleClear()

  defineExpose({
    focus,
    blur,
    clear,
    get select() {
      return inputRef.value?.$el || null
    },
    get wrapper() {
      return inputRef.value?.wrapper || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpSelect',
  }
</script>

<style scoped>
  .select-wrapper {
    position: relative;
    width: 100%;
  }

  .select-arrow {
    color: #6b7280;
    transition: transform 0.2s ease;
    cursor: pointer;
  }

  .select-arrow--reverse {
    transform: rotate(180deg);
  }

  /* 多选内容容器 */
  .sp-select__inner-content {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 100%;
    outline: none; /* 移除默认的焦点轮廓 */
    cursor: text; /* 显示文本光标 */
  }

  /* 多选模式下的输入框通过mode='display'控制，不需要额外隐藏 */

  /* 多选标签容器样式 */
  .sp-select__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    flex: 1;
    min-width: 0;
    align-items: center;
  }

  .sp-select__tag {
    flex-shrink: 0;
  }

  /* 占位符样式 */
  .sp-select__placeholder {
    color: #a8abb2;
    font-size: inherit;
    flex: 1;
  }

  /* 下拉框样式现在由 SelectDropdown 组件提供 */

  /* 覆盖 SelectDropdown 的动画效果，避免飘动 */
  :deep(.sp-select-dropdown-enter-active),
  :deep(.sp-select-dropdown-leave-active) {
    transition: none !important;
  }

  :deep(.sp-select-dropdown-enter-from),
  :deep(.sp-select-dropdown-leave-to) {
    opacity: 1 !important;
    transform: none !important;
  }

  /* 确保输入框在有标签时的样式调整 */
  :deep(.sp-input__inner) {
    flex: 1;
    min-width: 0;
  }

  /* 当有标签时，调整输入框内部布局 */
  :deep(.sp-input__wrapper) {
    min-height: auto;
  }

  :deep(.sp-input__inner-container) {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;
    min-height: inherit;
  }
</style>
