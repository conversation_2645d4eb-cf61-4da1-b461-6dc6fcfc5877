# Speed UI 主题自定义指南

Speed UI 提供了灵活的主题自定义系统，支持多种方式来定制你的应用主题。

## 🎨 主题自定义方案

### 方案 1: 使用 ConfigProvider（推荐）

这是最推荐的方式，类似于 Ant Design Vue 和 Naive UI 的做法。

```vue
<template>
  <SpConfigProvider :theme="themeConfig">
    <App />
  </SpConfigProvider>
</template>

<script setup>
import { SpConfigProvider } from '@speed-ui/ui'

const themeConfig = {
  primaryColor: '#1890ff',
  successColor: '#52c41a',
  warningColor: '#faad14',
  dangerColor: '#ff4d4f',
  borderRadius: '6px',
  fontSize: '16px',
}
</script>
```

### 方案 2: 使用组合式函数

```typescript
import { useTheme, setTheme, setPrimaryColor } from '@speed-ui/ui'

// 在组件中使用
const { theme, setTheme, setPrimaryColor } = useTheme()

// 设置预设主题
setTheme('blue')

// 设置自定义主色调
setPrimaryColor('#ff6b6b')

// 设置完整主题配置
setTheme({
  primaryColor: '#ff6b6b',
  successColor: '#52c41a',
  warningColor: '#faad14',
  dangerColor: '#ff4d4f',
})
```

### 方案 3: 直接使用 CSS 变量

```css
/* 全局设置 */
:root {
  --sp-color-primary: #1890ff;
  --sp-color-success: #52c41a;
  --sp-color-warning: #faad14;
  --sp-color-danger: #ff4d4f;
  --sp-border-radius: 6px;
  --sp-font-size: 16px;
}

/* 局部设置 */
.custom-theme {
  --sp-color-primary: #ff6b6b;
}
```

### 方案 4: 使用主题切换组件

```vue
<template>
  <SpThemeSwitcher
    :show-presets="true"
    :show-custom="true"
    :auto-apply-custom="true"
    @theme-change="handleThemeChange"
  />
</template>

<script setup>
import { SpThemeSwitcher } from '@speed-ui/ui'

const handleThemeChange = (theme) => {
  console.log('主题已切换:', theme)
}
</script>
```

## 📋 完整的主题配置选项

```typescript
interface ThemeConfig {
  /** 主色调 */
  primaryColor?: string
  /** 成功色 */
  successColor?: string
  /** 警告色 */
  warningColor?: string
  /** 危险色 */
  dangerColor?: string
  /** 信息色 */
  infoColor?: string
  /** 边框圆角 */
  borderRadius?: string
  /** 字体大小 */
  fontSize?: string
  /** 字体家族 */
  fontFamily?: string
}
```

## 🎯 预设主题

Speed UI 提供了 6 套预设主题：

```typescript
const PRESET_THEMES = {
  default: { primaryColor: '#667eea' },  // 默认紫色
  blue: { primaryColor: '#1890ff' },     // 蓝色
  green: { primaryColor: '#52c41a' },    // 绿色
  red: { primaryColor: '#ff4d4f' },      // 红色
  orange: { primaryColor: '#fa8c16' },   // 橙色
  purple: { primaryColor: '#722ed1' },   // 紫色
}
```

## 🔧 高级用法

### 动态主题切换

```typescript
import { useTheme } from '@speed-ui/ui'

const { theme, setTheme } = useTheme()

// 根据时间切换主题
const autoSwitchTheme = () => {
  const hour = new Date().getHours()
  if (hour >= 18 || hour <= 6) {
    setTheme('purple') // 夜间使用紫色主题
  } else {
    setTheme('blue')   // 白天使用蓝色主题
  }
}

// 根据用户偏好切换主题
const switchThemeByPreference = () => {
  const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  setTheme(isDark ? 'purple' : 'blue')
}
```

### 主题持久化

主题会自动保存到 localStorage，页面刷新后会自动恢复。

```typescript
// 手动保存主题
const saveTheme = (theme) => {
  localStorage.setItem('speed-ui-theme', JSON.stringify(theme))
}

// 手动加载主题
const loadTheme = () => {
  const saved = localStorage.getItem('speed-ui-theme')
  return saved ? JSON.parse(saved) : null
}
```

### 监听主题变化

```typescript
import { watch } from 'vue'
import { useTheme } from '@speed-ui/ui'

const { theme } = useTheme()

watch(theme, (newTheme) => {
  console.log('主题已变更:', newTheme)
  // 执行其他逻辑，如通知其他组件
}, { deep: true })
```

## 🎨 CSS 变量列表

Speed UI 使用以下 CSS 变量：

```css
/* 颜色变量 */
--sp-color-primary: #667eea;
--sp-color-primary-hover: #7c8aeb;
--sp-color-primary-active: #5a6ce8;
--sp-color-primary-disabled: #b8c5ff;
--sp-color-primary-lightest: #eef2ff;
--sp-color-primary-light: #d6e0ff;

--sp-color-success: #67c23a;
--sp-color-warning: #e6a23c;
--sp-color-danger: #f56c6c;
--sp-color-info: #909399;

/* 尺寸变量 */
--sp-border-radius: 4px;
--sp-font-size: 14px;
--sp-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
```

## 💡 最佳实践

### 1. 推荐的项目结构

```
src/
├── theme/
│   ├── index.ts          # 主题配置
│   ├── presets.ts        # 预设主题
│   └── variables.css     # CSS 变量
├── App.vue
└── main.ts
```

### 2. 主题配置文件

```typescript
// src/theme/index.ts
import { ThemeConfig } from '@speed-ui/ui'

export const lightTheme: ThemeConfig = {
  primaryColor: '#1890ff',
  successColor: '#52c41a',
  warningColor: '#faad14',
  dangerColor: '#ff4d4f',
}

export const darkTheme: ThemeConfig = {
  primaryColor: '#722ed1',
  successColor: '#52c41a',
  warningColor: '#faad14',
  dangerColor: '#ff4d4f',
}
```

### 3. 在 main.ts 中配置

```typescript
// src/main.ts
import { createApp } from 'vue'
import SpeedUI from '@speed-ui/ui'
import '@speed-ui/theme-default/lib/index.css'
import App from './App.vue'
import { lightTheme } from './theme'

const app = createApp(App)
app.use(SpeedUI)

// 设置默认主题
import { setTheme } from '@speed-ui/ui'
setTheme(lightTheme)

app.mount('#app')
```

## 🚀 迁移指南

### 从其他 UI 库迁移

#### 从 Element Plus 迁移

```typescript
// Element Plus
const theme = {
  'el-color-primary': '#409eff'
}

// Speed UI
const theme = {
  primaryColor: '#409eff'
}
```

#### 从 Ant Design Vue 迁移

```typescript
// Ant Design Vue
<a-config-provider :theme="{ token: { colorPrimary: '#00b96b' } }">

// Speed UI
<SpConfigProvider :theme="{ primaryColor: '#00b96b' }">
```

#### 从 Naive UI 迁移

```typescript
// Naive UI
const themeOverrides = {
  common: {
    primaryColor: '#FF0000'
  }
}

// Speed UI
const theme = {
  primaryColor: '#FF0000'
}
```

## 🔍 故障排除

### 主题不生效

1. 确保正确导入了 CSS 文件
2. 检查 CSS 变量是否被其他样式覆盖
3. 确认组件是否包含了 `sp-{component}-custom` 类名

### 颜色变化不平滑

确保添加了 CSS 过渡：

```css
:root {
  transition: 
    --sp-color-primary 0.3s ease,
    --sp-color-primary-hover 0.3s ease,
    --sp-color-primary-active 0.3s ease;
}
```

### 本地存储问题

如果主题持久化有问题，检查浏览器的本地存储权限和容量。
