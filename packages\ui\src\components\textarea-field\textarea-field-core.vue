<!--
  TextareaFieldCore.vue - 基于 FieldContainer 的 TextareaField 核心实现
  使用通用的 FieldContainer 提供浮动标签、表单验证等功能
-->

<template>
  <FieldContainer
    ref="fieldContainerRef"
    v-bind="containerProps"
    @update:value="handleValueUpdate"
    @input="handleInput"
    @change="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
    @keydown="handleKeydown"
    @wrapper-click="handleWrapperClick"
    @label-click="handleLabelClick"
    @validate="handleValidate"
  >
    <!-- 文本域输入元素 -->
    <template #default="slotProps">
      <textarea
        :id="slotProps.fieldId"
        ref="textareaRef"
        :value="value"
        :placeholder="slotProps.placeholder"
        :disabled="slotProps.disabled"
        :readonly="slotProps.readonly"
        :maxlength="maxlength"
        :rows="rows || 3"
        :cols="cols"
        :class="[slotProps.inputClasses, textareaSpecificClasses]"
        :style="{ ...slotProps.inputStyle, ...textareaStyle }"
        @input="slotProps.onInput"
        @change="slotProps.onChange"
        @focus="slotProps.onFocus"
        @blur="slotProps.onBlur"
        @keydown="slotProps.onKeydown"
      />
    </template>

    <!-- 功能区域（清除按钮和字数统计） -->
    <template
      #functions="slotProps"
      v-if="showClearIcon || showWordLimit"
    >
      <div :class="slotProps.functionsClasses">
        <!-- 清除按钮 -->
        <sp-icon
          v-if="showClearIcon"
          name="CloseCircle"
          :size="slotProps.iconSize"
          clickable
          :class="clearIconClasses"
          @click.stop.prevent="handleClear"
          @mousedown.stop.prevent
        />

        <!-- 字数统计 -->
        <span
          v-if="showWordLimit"
          :class="wordCountClasses"
        >
          {{ wordCount }}/{{ maxlength }}
        </span>
      </div>
    </template>
  </FieldContainer>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue'
  import SpIcon from '../icon/Icon.vue'
  import FieldContainer from '../field-container/FieldContainer.vue'
  import { useInputLogic } from '../../composables'
  import type { TextareaFieldProps, TextareaFieldEmits } from './types'
  import type { FieldContainerProps } from '../field-container/types'

  // ===== Props 和 Emits =====
  const props = withDefaults(defineProps<TextareaFieldProps>(), {
    value: '',
    variant: 'default',
    effect: 'none',
    size: 'medium',
    disabled: false,
    readonly: false,
    clearable: false,
    showWordLimit: false,
    error: false,
    loading: false,
    required: false,
    showMessage: true,
    persistentLabel: false,
    rows: 3,
    resize: 'vertical',
    autosize: false,
  })

  const emit = defineEmits<TextareaFieldEmits>()

  // ===== 组件引用 =====
  const fieldContainerRef = ref<InstanceType<typeof FieldContainer>>()
  const textareaRef = ref<HTMLTextAreaElement>()

  // ===== 使用 Input 逻辑 Composable（只用于特定功能） =====
  const {
    // 计算属性
    wordCount,

    // 显示逻辑
    showClearIcon,

    // 方法
    clear: clearTextarea,
  } = useInputLogic(props, emit)

  // ===== 传递给 FieldContainer 的属性 =====
  const containerProps = computed(() => ({
    value: props.value,
    label: props.label,
    name: props.name,
    placeholder: props.placeholder,
    disabled: props.disabled,
    readonly: props.readonly,
    required: props.required,
    error: props.error,
    loading: props.loading,
    variant: props.variant,
    effect: props.effect === 'shadow' ? 'glow' : props.effect, // 转换不支持的 effect
    size: props.size,
    rules: props.rules,
    showMessage: props.showMessage,
    helperText: props.helperText,
    persistentLabel: props.persistentLabel,
  }))

  // ===== Textarea 特有样式和功能 =====
  const textareaSpecificClasses = computed(() => [
    'sp-textarea-field__textarea',
  ])

  const textareaStyle = computed(() => ({
    resize: props.resize || 'vertical',
    ...(props.autosize && { overflow: 'hidden' }),
  }))

  const clearIconClasses = computed(() => ['sp-textarea-field__clear'])

  const wordCountClasses = computed(() => ['sp-textarea-field__count'])

  // ===== 自动调整高度功能 =====
  const adjustHeight = () => {
    if (!props.autosize || !textareaRef.value) return

    const textarea = textareaRef.value
    textarea.style.height = 'auto'

    let height = textarea.scrollHeight

    if (typeof props.autosize === 'object') {
      const { minRows = 1, maxRows } = props.autosize
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20

      if (minRows) {
        height = Math.max(height, minRows * lineHeight)
      }
      if (maxRows) {
        height = Math.min(height, maxRows * lineHeight)
      }
    }

    textarea.style.height = `${height}px`
  }

  // ===== 事件处理 =====
  const handleValueUpdate = (value: string | number | undefined) => {
    emit('update:value', value as string)
  }

  const handleInput = (event: Event) => {
    emit('input', event)
    // 自动调整高度
    if (props.autosize) {
      adjustHeight()
    }
  }

  const handleChange = (event: Event) => {
    emit('change', event)
  }

  const handleFocus = (event: FocusEvent) => {
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    emit('blur', event)
  }

  const handleKeydown = (event: KeyboardEvent) => {
    emit('keydown', event)
  }

  const handleWrapperClick = () => {
    if (textareaRef.value && !props.disabled && !props.readonly) {
      textareaRef.value.focus()
    }
  }

  const handleLabelClick = () => {
    if (textareaRef.value && !props.disabled && !props.readonly) {
      textareaRef.value.focus()
    }
  }

  const handleClear = () => {
    clearTextarea()
    emit('clear')
  }

  const handleValidate = (name: string, isValid: boolean, message: string) => {
    emit('validate', name, isValid, message)
  }

  // ===== 暴露的方法 =====
  const focus = () => {
    if (textareaRef.value) {
      textareaRef.value.focus()
    }
  }

  const blur = () => {
    if (textareaRef.value) {
      textareaRef.value.blur()
    }
  }

  const select = () => {
    if (textareaRef.value) {
      textareaRef.value.select()
    }
  }

  const clear = () => {
    handleClear()
  }

  const validate = async (): Promise<boolean> => {
    return (await fieldContainerRef.value?.validate()) || false
  }

  const resetField = () => {
    fieldContainerRef.value?.resetField()
  }

  const clearValidate = () => {
    fieldContainerRef.value?.clearValidate()
  }

  // ===== 暴露方法 =====
  defineExpose({
    focus,
    blur,
    select,
    clear,
    validate,
    resetField,
    clearValidate,
    adjustHeight,
    get textarea() {
      return textareaRef.value || null
    },
    get wrapper() {
      return fieldContainerRef.value?.wrapper || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'TextareaFieldCore',
  }
</script>
