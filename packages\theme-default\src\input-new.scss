/**
 * Input 组件样式 (新架构)
 * 基于 Ant Design Vue 架构优化的样式系统
 */

@use './common/var.scss' as *;

// Input 新架构变量
$input-affix-padding: 8px !default;
$input-icon-size: 16px !default;
$input-clear-size: 14px !default;

// Input Affix Wrapper - 布局容器
.sp-input-affix {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 100%;
  min-width: 0;
  padding: 8px 12px;
  color: $color-text-primary;
  font-size: $font-size-base;
  line-height: $line-height-base;
  background-color: $background-color-base;
  background-image: none;
  border: 1px solid $border-color-base;
  border-radius: $border-radius-base;
  transition: all $transition-duration-base $transition-timing-base;
  box-sizing: border-box;

  // 尺寸变体
  &--small {
    padding: 4px 8px;
    font-size: $font-size-sm;

    .sp-input-affix__prefix,
    .sp-input-affix__suffix {
      font-size: $font-size-sm;
    }
  }

  &--medium {
    padding: 8px 12px;
    font-size: $font-size-base;
  }

  &--large {
    padding: 12px 16px;
    font-size: $font-size-lg;

    .sp-input-affix__prefix,
    .sp-input-affix__suffix {
      font-size: $font-size-lg;
    }
  }

  // 状态变体
  &:hover {
    border-color: $border-color-hover;
  }

  &--focused,
  &:focus-within {
    outline: none;
    border-color: $color-primary;
    box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
  }

  &--disabled {
    color: $color-text-disabled;
    background-color: $background-color-disabled;
    border-color: $border-color-base;
    cursor: not-allowed;

    // 禁用状态下的悬停效果 - 保持边框颜色不变
    &:hover {
      border-color: $border-color-base;
      background-color: $background-color-disabled;
    }

    // 禁用聚焦效果
    &:focus,
    &:focus-within,
    &:active {
      border-color: $border-color-base;
      background-color: $background-color-disabled;
      box-shadow: none;
      outline: none;
    }

    .sp-input-affix__input,
    .sp-input-affix__prefix,
    .sp-input-affix__suffix {
      color: $color-text-disabled;
      cursor: not-allowed;
      pointer-events: none;

      &:hover,
      &:focus,
      &:active {
        color: $color-text-disabled;
      }
    }

    // 禁用占位符样式
    .sp-input-affix__input::placeholder {
      color: $color-text-disabled;
      opacity: 0.6;
    }
  }

  &--readonly {
    .sp-input-affix__input {
      cursor: default;
    }
  }

  // 验证状态
  &--error {
    border-color: $color-error;

    &:hover {
      border-color: $color-error;
    }

    &--focused,
    &:focus-within {
      border-color: $color-error;
      box-shadow: 0 0 0 2px rgba($color-error, 0.2);
    }
  }

  &--warning {
    border-color: $color-warning;

    &:hover {
      border-color: $color-warning;
    }

    &--focused,
    &:focus-within {
      border-color: $color-warning;
      box-shadow: 0 0 0 2px rgba($color-warning, 0.2);
    }
  }

  &--success {
    border-color: $color-success;

    &:hover {
      border-color: $color-success;
    }

    &--focused,
    &:focus-within {
      border-color: $color-success;
      box-shadow: 0 0 0 2px rgba($color-success, 0.2);
    }
  }

  // 无边框模式
  &:not(.sp-input-affix--bordered) {
    border: none;
    box-shadow: none;
    background-color: transparent;

    &:hover,
    &.sp-input-affix--focused,
    &:focus-within {
      box-shadow: none;
    }
  }

  // 前缀
  &__prefix {
    display: flex;
    align-items: center;
    margin-right: $input-affix-padding;
    color: $color-text-secondary;
    flex-shrink: 0;

    &--small {
      margin-right: 6px;
    }

    &--large {
      margin-right: 10px;
    }
  }

  // 后缀
  &__suffix {
    display: flex;
    align-items: center;
    margin-left: $input-affix-padding;
    color: $color-text-secondary;
    flex-shrink: 0;

    &--small {
      margin-left: 6px;
    }

    &--large {
      margin-left: 10px;
    }
  }

  // 核心输入区域
  &__input {
    flex: 1;
    min-width: 0;
    border: none;
    outline: none;
    background: transparent;
    color: inherit;
    font-size: inherit;
    line-height: inherit;
    padding: 0;

    &::placeholder {
      color: $color-text-placeholder;
    }

    &::-webkit-input-placeholder {
      color: $color-text-placeholder;
    }

    &::-moz-placeholder {
      color: $color-text-placeholder;
    }

    &:-ms-input-placeholder {
      color: $color-text-placeholder;
    }
  }

  // 图标
  &__icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: $input-icon-size;
    height: $input-icon-size;
    font-size: $input-icon-size;
  }

  // 清除按钮
  &__clear {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: $input-clear-size;
    height: $input-clear-size;
    margin-left: 4px;
    color: $color-text-secondary;
    font-size: $input-clear-size;
    cursor: pointer;
    transition: color $transition-duration-base $transition-timing-base;
    border-radius: 50%;
    flex-shrink: 0;

    // 确保内部图标完全居中
    .sp-icon {
      display: flex !important;
      align-items: center;
      justify-content: center;
      vertical-align: baseline !important; // 覆盖Icon组件的middle设置
    }

    &:hover {
      color: $color-text-primary;
      background-color: rgba($color-text-secondary, 0.1);
    }

    &--small {
      width: 12px;
      height: 12px;
      font-size: 12px;
    }

    &--large {
      width: 16px;
      height: 16px;
      font-size: 16px;
    }
  }

  // 密码切换按钮
  &__password {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: $input-icon-size;
    height: $input-icon-size;
    margin-left: 4px;
    color: $color-text-secondary;
    font-size: $input-icon-size;
    cursor: pointer;
    transition: color $transition-duration-base $transition-timing-base;

    &:hover {
      color: $color-text-primary;
    }

    &--visible {
      color: $color-primary;
    }

    &--small {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }

    &--large {
      width: 18px;
      height: 18px;
      font-size: 18px;
    }
  }

  // 帮助按钮
  &__help {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: $input-icon-size;
    height: $input-icon-size;
    margin-left: 4px;
    color: $color-text-secondary;
    font-size: $input-icon-size;
    cursor: pointer;
    transition: color $transition-duration-base $transition-timing-base;
    border-radius: 50%;

    &:hover {
      color: $color-primary;
      background-color: rgba($color-primary, 0.1);
    }

    &--small {
      width: 12px;
      height: 12px;
      font-size: 12px;
    }

    &--medium {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }

    &--large {
      width: 16px;
      height: 16px;
      font-size: 16px;
    }
  }

  // 字符计数
  &__count {
    position: absolute;
    bottom: -20px;
    right: 0;
    color: $color-text-secondary;
    font-size: $font-size-sm;
    line-height: 1;

    &--error {
      color: $color-error;
    }

    &--small {
      font-size: 11px;
    }

    &--large {
      font-size: $font-size-base;
    }
  }
}

// Input Core - 核心控制层样式
.sp-input-core {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  color: inherit;
  font-size: inherit;
  line-height: inherit;
  padding: 0;

  &--disabled {
    cursor: not-allowed;
  }

  &--readonly {
    cursor: default;
  }
}

// 组合样式
.sp-input-affix {
  // 当有前缀时
  &--has-prefix {
    .sp-input-affix__input {
      // 前缀已经通过 margin 处理间距，这里不需要额外处理
    }
  }

  // 当有后缀时
  &--has-suffix {
    .sp-input-affix__input {
      // 后缀已经通过 margin 处理间距，这里不需要额外处理
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .sp-input-affix {
    &--large {
      padding: 10px 14px;
      font-size: $font-size-base;
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .sp-input-affix {
    background-color: $background-color-dark;
    border-color: $border-color-dark;
    color: $color-text-dark;

    &:hover {
      border-color: $border-color-hover-dark;
    }

    &--focused,
    &:focus-within {
      border-color: $color-primary-dark;
      box-shadow: 0 0 0 2px rgba($color-primary-dark, 0.2);
    }

    .sp-input-affix__input::placeholder {
      color: $color-text-placeholder-dark;
    }
  }
}
