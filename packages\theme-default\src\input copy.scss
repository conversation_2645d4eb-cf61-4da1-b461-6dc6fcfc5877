// ================================
// Speed UI Input 组件样式
// ================================

@use './common/var.scss' as *;

.sp-input {
  position: relative;
  font-size: 14px;
  display: inline-block;
  width: 100%;

  &--small {
    font-size: 12px;
  }

  &--large {
    font-size: 16px;
  }

  // 输入框包装器
  &__wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    // padding: 8px 12px;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    background-color: $background-color-base;
    transition: all $transition-duration-base $transition-timing-base;
    box-sizing: border-box;

    &:hover {
      border-color: $border-color-hover;
    }

    &:focus-within {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
    }

    // 禁用状态
    &--disabled {
      background-color: $background-color-disabled;
      border-color: $border-color-base;
      color: $color-text-disabled;
      cursor: not-allowed;

      &:hover {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
      }

      &:focus-within {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
        box-shadow: none;
        outline: none;
      }

      // 禁用状态下的内部输入框
      .sp-input__inner {
        background-color: transparent;
        color: $color-text-disabled;
        cursor: not-allowed;

        &::placeholder {
          color: $color-text-disabled;
        }
      }

      // 禁用状态下的前后缀
      .sp-input__prefix,
      .sp-input__suffix {
        color: $color-text-disabled;
      }
    }

    // 当wrapper作为容器时，内部的input应该无边框无内边距
    .sp-input__inner {
      border: none;
      padding: 0;
      background: transparent;
      flex: 1;
      min-width: 0; // 防止flex item溢出

      &:focus {
        outline: none;
        border: none;
        box-shadow: none;
      }

      &:hover {
        border: none;
      }
    }
  }

  // 输入框主体（保持向后兼容）
  &__inner {
    width: 100%;
    // padding: 8px 12px;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    font-size: inherit;
    line-height: 1.5;
    color: $color-text-primary;
    background-color: $background-color-base;
    background-image: none;
    transition: all $transition-duration-base $transition-timing-base;
    box-sizing: border-box;
    position: relative;

    // 当有前缀或后缀图标时，移除内边距，让flex布局处理间距
    &--has-prefix {
      padding-left: 0;
    }

    &--has-suffix {
      padding-right: 0;
    }

    &::placeholder {
      color: $color-text-disabled;
    }

    &:hover {
      border-color: $border-color-hover;
    }

    &:focus {
      outline: none;
      border-color: $color-primary;
      box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
      // 移除 box-shadow，使用简洁的聚焦效果
    }

    &::before {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border-radius: $border-radius-base;
      background: linear-gradient(135deg, $color-primary 0%, $color-info 100%);
      z-index: -1;
      opacity: 0;
      transition: opacity $transition-duration-base ease;
    }
  }

  // 文本域
  &__textarea {
    width: 100%;
    min-height: 32px;
    padding: 8px 12px;
    border: 1px solid $border-color-base;
    border-radius: $border-radius-base;
    background-color: $background-color-base;
    color: $color-text-primary;
    font-size: inherit;
    font-family: inherit;
    line-height: 1.5;
    resize: vertical;
    outline: none;
    box-sizing: border-box;
    transition: none; // 移除过渡效果，避免拖拽延迟

    &::placeholder {
      color: $color-text-disabled;
    }

    &:hover {
      border-color: $border-color-hover;
    }

    &:focus {
      border-color: $color-primary;
      // 移除 box-shadow，使用简洁的聚焦效果
    }

    // 只在非拖拽状态下应用过渡
    &:not(:active) {
      transition: border-color 0.2s $transition-timing-base;
    }

    // 拖拽时的性能优化
    &:active {
      will-change: height; // 提示浏览器即将改变高度
      pointer-events: auto; // 确保拖拽事件正常
    }

    // 拖拽结束后清理
    &:not(:active) {
      will-change: auto;
    }
  }

  // 尺寸变体 - 针对 wrapper 的尺寸修饰符
  &__wrapper--small {
    padding: 4px 8px;
    font-size: 12px;

    .sp-input__prefix,
    .sp-input__suffix {
      font-size: 12px;
    }
  }

  &__wrapper--medium {
    // padding: 8px 12px;
    font-size: 14px;

    .sp-input__prefix,
    .sp-input__suffix {
      font-size: 14px;
    }
  }

  &__wrapper--large {
    padding: 12px 16px;
    font-size: 16px;

    .sp-input__prefix,
    .sp-input__suffix {
      font-size: 16px;
    }
  }

  // 兼容旧的尺寸变体（保留向后兼容性）
  &--small &__inner {
    padding: 6px 8px;
    font-size: 12px;
  }

  &--large &__inner {
    padding: 12px 16px;
    font-size: 16px;
  }

  // 前缀和后缀 - 使用flexbox布局
  &__prefix,
  &__suffix {
    display: flex;
    align-items: center;
    color: $color-text-disabled;
    flex-shrink: 0; // 防止图标被压缩
  }

  &__prefix {
    margin-right: 8px; // 前缀图标右边距
  }

  &__suffix {
    margin-left: 8px; // 后缀图标左边距
  }

  // 保持向后兼容的绝对定位样式（当不使用wrapper时）
  &:not(&--has-wrapper) {
    .sp-input__prefix,
    .sp-input__suffix {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;
    }

    .sp-input__prefix {
      left: 8px;
      margin-right: 0;
    }

    .sp-input__suffix {
      right: 8px;
      margin-left: 0;
    }

    &.sp-input--prefix .sp-input__inner {
      padding-left: 40px;
    }

    &.sp-input--suffix .sp-input__inner {
      padding-right: 40px !important;
    }
  }

  // 图标
  &__icon {
    cursor: pointer;
    font-size: 14px;
    margin-left: 4px;
    transition: color 0.2s;
    display: inline-flex;
    align-items: center;
    justify-content: center;

    // xicons SVG 图标样式
    svg {
      width: 1em;
      height: 1em;
      fill: currentColor;
      transition: all $transition-duration-base cubic-bezier(0.25, 0.8, 0.25, 1);
    }

    &:hover {
      color: $color-text-secondary;
    }

    &:first-child {
      margin-left: 0;
    }
  }

  // 清除按钮
  &__clear {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    margin-left: 4px;
    color: $color-text-secondary;
    font-size: 12px;
    cursor: pointer;
    transition: color $transition-duration-base $transition-timing-base;
    border-radius: 50%;
    flex-shrink: 0; // 防止被压缩

    // 确保内部图标完全居中
    .sp-icon {
      display: flex !important;
      align-items: center;
      justify-content: center;
      vertical-align: baseline !important; // 覆盖Icon组件的middle设置
    }

    &:hover {
      color: $color-primary;
      background-color: rgba($color-primary, 0.1);
    }

    &--small {
      width: 12px;
      height: 12px;
      font-size: 10px;
    }

    &--medium {
      width: 14px;
      height: 14px;
      font-size: 12px;
    }

    &--large {
      width: 16px;
      height: 16px;
      font-size: 14px;
    }
  }

  &__password {
    &:hover {
      color: $color-primary;
    }
  }

  // 帮助按钮
  &__help {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    margin-left: 4px;
    color: $color-text-secondary;
    font-size: 14px;
    cursor: pointer;
    transition: color $transition-duration-base $transition-timing-base;
    border-radius: 50%;

    &:hover {
      color: $color-primary;
      background-color: rgba($color-primary, 0.1);
    }

    &--small {
      width: 12px;
      height: 12px;
      font-size: 12px;
    }

    &--medium {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }

    &--large {
      width: 16px;
      height: 16px;
      font-size: 16px;
    }

    // 图标样式
    svg {
      width: 1em;
      height: 1em;
      fill: currentColor;
    }
  }

  // 内部字符计数
  &__count-in {
    font-size: 12px;
    color: $color-text-secondary;
    line-height: 1;
    margin-left: 8px;
    white-space: nowrap;
    flex-shrink: 0;

    &--error {
      color: #ff4d4f;
    }

    &--small {
      font-size: 11px;
      margin-left: 6px;
    }

    &--large {
      font-size: 13px;
      margin-left: 10px;
    }
  }

  // 外部字符计数
  &__count-out {
    position: absolute;
    bottom: -20px;
    right: 0;
    font-size: 12px;
    color: $color-text-secondary;

    &--error {
      color: #ff4d4f;
    }

    &--small {
      font-size: 11px;
    }

    &--large {
      font-size: 13px;
    }
  }

  // 保留向后兼容的字符计数样式
  &__count {
    position: absolute;
    bottom: -20px;
    right: 0;
    font-size: 12px;
    color: $color-text-secondary;
  }

  &--textarea &__count {
    position: absolute;
    bottom: 8px;
    right: 12px;
    background: rgba(255, 255, 255, 0.9);
    padding: 0 4px;
    border-radius: $border-radius-small;
  }

  // Slip 动画标签
  &__slip-label {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: $color-text-disabled;
    font-size: inherit;
    pointer-events: none;
    transition: all $transition-duration-base cubic-bezier(0.25, 0.8, 0.25, 1);
    background: transparent;
    padding: 0 2px;
    z-index: 1;
    transform-origin: left center;
    line-height: 1;

    &--active {
      top: 0;
      transform: translateY(-50%) scale(0.85);
      color: $color-primary;
      font-weight: 500;
      background: $background-color-base;
      padding: 0 2px;
    }
  }

  // 修正slip模式下的样式
  &__inner:focus + &__slip-label,
  &__inner:not(:placeholder-shown) + &__slip-label {
    top: 0;
    transform: translateY(-50%) scale(0.85);
    color: $color-primary;
    font-weight: 500;
    background: $background-color-base;
    padding: 0 2px;
  }

  // 有内容但失去焦点时的颜色
  &__inner:not(:placeholder-shown) + &__slip-label {
    color: $color-text-secondary;
  }

  // 聚焦且有内容时的颜色
  &__inner:focus:not(:placeholder-shown) + &__slip-label {
    color: $color-primary;
  }

  // Slip 模式下的输入框样式调整
  &--slip {
    &.sp-input--prefix .sp-input__slip-label {
      left: 30px;
    }

    &.sp-input--small .sp-input__slip-label {
      left: 8px;
      font-size: 12px;

      &--active {
        transform: translateY(-50%) scale(0.8);
      }
    }

    &.sp-input--large .sp-input__slip-label {
      left: 16px;
      font-size: 16px;

      &--active {
        transform: translateY(-50%) scale(0.9);
      }
    }
  }

  // 状态样式
  &--disabled {
    // 禁用 wrapper 的交互状态
    .sp-input__wrapper {
      background-color: $background-color-disabled;
      border-color: $border-color-base;
      color: $color-text-disabled;
      cursor: not-allowed;

      // 禁用状态下的悬停效果 - 保持边框颜色不变
      &:hover {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
      }

      // 禁用聚焦效果
      &:focus,
      &:focus-within,
      &:active {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
        box-shadow: none;
        outline: none;
      }
    }

    .sp-input__inner {
      background-color: $background-color-disabled;
      border-color: $border-color-base;
      color: $color-text-disabled;
      cursor: not-allowed;

      // 禁用状态下的悬停效果
      &:hover {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
      }

      // 禁用聚焦效果
      &:focus,
      &:focus-within,
      &:active {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
        box-shadow: none;
        outline: none;
      }

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    // 禁用所有图标和按钮的交互
    .sp-input__icon,
    .sp-input__clear,
    .sp-input__password,
    .sp-input__prefix,
    .sp-input__suffix {
      color: $color-text-disabled;
      cursor: not-allowed;
      pointer-events: none;

      &:hover,
      &:focus,
      &:active {
        color: $color-text-disabled;
      }
    }
  }

  &--readonly {
    .sp-input__inner {
      background-color: $background-color-hover;
      border-color: $border-color-base;
      cursor: default;
    }
  }

  &--focused {
    .sp-input__inner {
      border-color: $color-primary;
      // box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2),
      //   inset 0 0 0 1px $color-primary;
    }
  }

  // 验证状态 - 只设置边框颜色，不设置阴影
  &--success {
    .sp-input__inner {
      border-color: $color-success;

      &:hover {
        border-color: $color-success-active;
      }

      &:focus {
        border-color: $color-success;
        box-shadow: 0 0 0 2px rgba($color-success, 0.2),
          inset 0 0 0 1px $color-success;
      }
    }
  }

  &--warning {
    .sp-input__inner {
      border-color: $color-warning;

      &:hover {
        border-color: $color-warning-active;
      }

      &:focus {
        border-color: $color-warning;
        box-shadow: 0 0 0 2px rgba($color-warning, 0.2),
          inset 0 0 0 1px $color-warning;
      }
    }
  }

  &--error {
    .sp-input__inner {
      border-color: $color-danger;

      &:hover {
        border-color: $color-danger-active;
      }

      &:focus {
        border-color: $color-danger;
        box-shadow: 0 0 0 2px rgba($color-danger, 0.2),
          inset 0 0 0 1px $color-danger;
      }
    }
  }

  // 验证消息
  &__validate-message {
    position: absolute;
    top: 100%;
    left: 0;
    margin-top: 4px;
    font-size: 12px;
    line-height: 1;

    &--success {
      color: $color-success;
    }

    &--warning {
      color: $color-warning;
    }

    &--error {
      color: $color-danger;
    }
  }

  // 文本域特殊样式
  &--textarea {
    .sp-input__wrapper {
      display: block;
    }
  }

  // 文本线模式样式
  &--textline {
    .sp-input__wrapper {
      border: none;
      border-bottom: 2px solid $border-color-base;
      border-radius: 0;
      padding: 0;
      background: transparent;
    }

    .sp-input__inner {
      padding: 8px 0;
      border: none;
      background: transparent;
      transition: border-color $transition-duration-base;

      &:focus {
        outline: none;
        box-shadow: none;
      }
    }

    &.sp-input--focused .sp-input__wrapper {
      border-bottom-color: $color-primary;
    }
  }

  // 格子输入模式样式
  &--texttabel {
    .sp-input__tabel {
      display: flex;
      gap: 8px;
    }

    .sp-input__tabel-cell {
      width: 32px;
      height: 40px;
      text-align: center;
      font-size: 20px;
      border: 1.5px solid $border-color-base;
      border-radius: $border-radius-base;
      outline: none;
      transition: border-color $transition-duration-base;
      background-color: $background-color-base;
      color: $color-text-primary;

      &:focus {
        border-color: $color-primary;
        // 移除粗阴影，使用简洁的聚焦效果
      }

      &:disabled {
        background-color: $background-color-hover;
        color: $color-text-disabled;
        cursor: not-allowed;
      }
    }
  }

  // 聚焦状态样式
  &--focused {
    .sp-input__wrapper {
      border-color: $color-primary;
      // 移除粗阴影，使用简洁的聚焦效果
    }

    .sp-input__textarea {
      border-color: $color-primary;
      // 移除粗阴影，使用简洁的聚焦效果
    }

    .sp-input__inner {
      border-color: $color-primary;
      // 移除粗阴影，使用简洁的聚焦效果
    }
  }

  &--disabled {
    .sp-input__wrapper {
      background-color: $background-color-hover;
      border-color: $border-color-base;
      color: $color-text-disabled;
      cursor: not-allowed;
    }

    .sp-input__inner,
    .sp-input__textarea {
      background-color: $background-color-hover;
      border-color: $border-color-base;
      color: $color-text-disabled;
      cursor: not-allowed;
    }
  }

  &--readonly {
    .sp-input__inner,
    .sp-input__textarea {
      background-color: $background-color-hover;
      border-color: $border-color-base;
      cursor: default;
    }
  }

  // 验证状态样式 - 为 wrapper 和 textarea 添加样式
  &--error {
    .sp-input__wrapper {
      border-color: $color-danger;

      &:hover {
        border-color: $color-danger-active;
      }
    }

    .sp-input__textarea {
      border-color: $color-danger;

      &:hover {
        border-color: $color-danger-active;
      }

      &:focus {
        border-color: $color-danger;
        box-shadow: 0 0 0 2px rgba($color-danger, 0.2);
      }
    }
  }

  &--warning {
    .sp-input__wrapper {
      border-color: $color-warning;

      &:hover {
        border-color: $color-warning-active;
      }
    }

    .sp-input__textarea {
      border-color: $color-warning;

      &:hover {
        border-color: $color-warning-active;
      }

      &:focus {
        border-color: $color-warning;
        box-shadow: 0 0 0 2px rgba($color-warning, 0.2);
      }
    }
  }

  &--success {
    .sp-input__wrapper {
      border-color: $color-success;

      &:hover {
        border-color: $color-success-active;
      }
    }

    .sp-input__textarea {
      border-color: $color-success;

      &:hover {
        border-color: $color-success-active;
      }

      &:focus {
        border-color: $color-success;
        box-shadow: 0 0 0 2px rgba($color-success, 0.2);
      }
    }
  }

  // 聚焦状态和验证状态组合
  // &--focused {
  //   &.sp-input--error {
  //     .sp-input__wrapper {
  //       border-color: $color-danger-active;
  //       box-shadow: 0 0 0 2px rgba($color-danger, 0.2);
  //     }

  //     .sp-input__textarea {
  //       border-color: $color-danger-active;
  //       box-shadow: 0 0 0 2px rgba($color-danger, 0.2);
  //     }

  //     .sp-input__inner {
  //       border-color: $color-danger;
  //       box-shadow: 0 0 0 2px rgba($color-danger, 0.2),
  //         inset 0 0 0 1px $color-danger;
  //     }
  //   }

  //   &.sp-input--warning {
  //     .sp-input__wrapper {
  //       border-color: $color-warning-active;
  //       box-shadow: 0 0 0 2px rgba($color-warning, 0.2);
  //     }

  //     .sp-input__textarea {
  //       border-color: $color-warning-active;
  //       box-shadow: 0 0 0 2px rgba($color-warning, 0.2);
  //     }

  //     .sp-input__inner {
  //       border-color: $color-warning;
  //       box-shadow: 0 0 0 2px rgba($color-warning, 0.2),
  //         inset 0 0 0 1px $color-warning;
  //     }
  //   }

  //   &.sp-input--success {
  //     .sp-input__wrapper {
  //       border-color: $color-success-active;
  //       box-shadow: 0 0 0 2px rgba($color-success, 0.2);
  //     }

  //     .sp-input__textarea {
  //       border-color: $color-success-active;
  //       box-shadow: 0 0 0 2px rgba($color-success, 0.2);
  //     }

  //     .sp-input__inner {
  //       border-color: $color-success;
  //       box-shadow: 0 0 0 2px rgba($color-success, 0.2),
  //         inset 0 0 0 1px $color-success;
  //     }
  //   }
  // }
}

// ================================
// InputNumber 数字输入框样式
// ================================

.sp-input-number {
  // 继承基础输入框样式
  @extend .sp-input;

  // 数字输入框特有样式
  .sp-input__inner {
    text-align: left;

    // 移除浏览器默认的数字输入框样式
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }

    // Firefox
    &[type='number'] {
      -moz-appearance: textfield;
      appearance: textfield;
    }
  }

  // 仅整数模式
  &--integer-only {
    .sp-input__inner {
      font-variant-numeric: tabular-nums;
    }
  }

  // 仅正数模式
  &--positive-only {
    .sp-input__inner {
      font-variant-numeric: tabular-nums;
    }
  }

  // 禁用状态 - 移除过度的强制样式，采用 Ant Design 的策略
  &--disabled {
    .sp-input__wrapper {
      background-color: $background-color-disabled;
      border-color: $border-color-base;
      color: $color-text-disabled;
      cursor: not-allowed;

      &:hover {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
      }

      &:focus,
      &:focus-within,
      &:active {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
        box-shadow: none;
        outline: none;
      }
    }

    .sp-input__inner,
    .sp-input__textarea,
    .sp-input__input {
      background-color: $background-color-disabled;
      border-color: $border-color-base;
      color: $color-text-disabled;
      cursor: not-allowed;

      &:hover {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
      }

      &:focus,
      &:focus-within,
      &:active {
        border-color: $border-color-base;
        background-color: $background-color-disabled;
        box-shadow: none;
        outline: none;
      }

      &::placeholder {
        color: $color-text-disabled;
      }
    }

    // 禁用所有图标和按钮的交互
    .sp-input__icon,
    .sp-input__clear,
    .sp-input__password,
    .sp-input__prefix,
    .sp-input__suffix {
      color: $color-text-disabled;
      cursor: not-allowed;
      pointer-events: none;

      &:hover,
      &:focus,
      &:active {
        color: $color-text-disabled;
      }
    }

    // 禁用字符计数的交互
    .sp-input__count-in,
    .sp-input__count-out {
      color: $color-text-disabled;
      pointer-events: none;
    }
  }
}
