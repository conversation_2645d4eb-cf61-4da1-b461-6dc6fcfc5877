<template>
  <aside class="custom-sidebar" :class="{ 'dark-theme': isDark }">
    <div class="sidebar-container">
      <!-- 侧边栏标题 -->
      <div class="sidebar-header">
        <h3 class="sidebar-title">导航菜单</h3>
      </div>

      <!-- 动态导航菜单 -->
      <nav class="sidebar-nav">
        <div v-for="section in sidebarSections" :key="section.path" class="nav-section">
          <h4 class="section-title">{{ section.title }}</h4>
          <div v-for="group in section.groups" :key="group.text" class="nav-group">
            <h5 v-if="group.text" class="group-title">{{ group.text }}</h5>
            <ul class="nav-list">
              <li v-for="item in group.items" :key="item.link" class="nav-item">
                <a :href="item.link" class="nav-link" :class="{ active: isActiveRoute(item.link) }">
                  <span class="nav-icon">{{ getIcon(item.text, section.path) }}</span>
                  <span class="nav-text">{{ item.text }}</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </nav>
    </div>
  </aside>
</template>

<script setup>
import { useRoute, useData } from 'vitepress'
import { computed, onMounted, ref } from 'vue'

const route = useRoute()
const { theme } = useData()
const isDark = ref(false)

// 检查当前路由是否激活
const isActiveRoute = (path) => {
  const currentPath = route.path
  // 精确匹配或者当前路径以该路径开头（但要确保是完整的路径段匹配）
  if (currentPath === path) {
    return true
  }
  
  // 处理带扩展名的情况，如 /components/button.html 应该匹配 /components/button
  const pathWithoutExt = currentPath.replace(/\.(html|md)$/, '')
  if (pathWithoutExt === path) {
    return true
  }
  
  // 处理子路径的情况
  if (currentPath.startsWith(path + '/')) {
    return true
  }
  
  return false
}

// 获取当前路径对应的侧边栏配置
const sidebarSections = computed(() => {
  const sidebar = theme.value.sidebar
  if (!sidebar) return []
  
  const currentPath = route.path
  let matchedSidebar = null
  
  // 查找匹配的侧边栏配置
  for (const [path, config] of Object.entries(sidebar)) {
    if (currentPath.startsWith(path)) {
      matchedSidebar = { path, config }
      break
    }
  }
  
  // 只有在匹配到特定路径时才显示侧边栏
  if (!matchedSidebar) return []
  
  // 转换配置为组件需要的格式
  const sections = []
  
  if (matchedSidebar.path === '/guide/') {
    sections.push({
      path: '/guide/',
      title: '指南',
      groups: matchedSidebar.config
    })
  } else if (matchedSidebar.path === '/components/') {
    sections.push({
      path: '/components/',
      title: '组件',
      groups: matchedSidebar.config
    })
  }
  
  return sections
})

// 根据文本和路径获取图标
const getIcon = (text, sectionPath) => {
  if (sectionPath === '/guide/') {
    if (text.includes('快速开始')) return '📚'
    if (text.includes('安装')) return '⚙️'
    if (text.includes('主题定制')) return '🎨'
    return '📖'
  } else if (sectionPath === '/components/') {
    if (text.includes('Button') || text.includes('按钮')) return '🔘'
    if (text.includes('Input') || text.includes('输入')) return '📝'
    if (text.includes('Card') || text.includes('卡片')) return '🃏'
    if (text.includes('Dialog') || text.includes('对话')) return '💬'
    if (text.includes('Table') || text.includes('表格')) return '📊'
    if (text.includes('Form') || text.includes('表单')) return '📋'
    if (text.includes('Select') || text.includes('选择')) return '📋'
    if (text.includes('Radio') || text.includes('单选')) return '🔘'
    if (text.includes('Checkbox') || text.includes('复选')) return '☑️'
    if (text.includes('Switch') || text.includes('开关')) return '🔄'
    if (text.includes('Upload') || text.includes('上传')) return '📤'
    return '🧩'
  }
  return '📄'
}

// 初始化主题
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  isDark.value = savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)
  
  // 监听主题变化
  const observer = new MutationObserver(() => {
    isDark.value = document.documentElement.classList.contains('dark')
  })
  
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })
})
</script>

<style scoped>
.custom-sidebar {
  position: fixed;
  top: 60px; /* 为顶部导航栏留出空间 */
  left: 0;
  width: 280px;
  height: calc(100vh - 60px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: saturate(50%) blur(8px);
  -webkit-backdrop-filter: saturate(50%) blur(8px);
  border-right: 1px solid #e4e7ed;
  z-index: 999;
  overflow-y: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-container {
  padding: 24px 0;
  height: 100%;
}

/* 侧边栏标题 */
.sidebar-header {
  padding: 0 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.sidebar-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  letter-spacing: -0.02em;
}

/* 导航区域 */
.sidebar-nav {
  padding: 0 16px;
}

.nav-section {
  margin-bottom: 32px;
}

.section-title {
  margin: 0 0 12px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #909399;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.group-title {
  font-size: 13px;
  font-weight: 500;
  color: var(--vp-c-text-2);
  margin: 16px 0 8px 0;
  padding: 0 16px;
  opacity: 0.7;
}

.nav-group:first-child .group-title {
  margin-top: 0;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #606266;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #409eff, #67c23a);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.nav-link:hover {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.nav-link:hover::before {
  opacity: 0.05;
}

.nav-link.active {
  color: #409eff;
  background: rgba(64, 158, 255, 0.15);
  font-weight: 600;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.nav-link.active::before {
  opacity: 0.1;
}

.nav-link:active {
  transform: translateX(2px);
  transition: transform 0.1s;
}

.nav-link:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.3);
}

.nav-link:focus:not(:focus-visible) {
  box-shadow: none;
}

.nav-icon {
  font-size: 16px;
  flex-shrink: 0;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: -0.01em;
}

/* 滚动条样式 */
.custom-sidebar::-webkit-scrollbar {
  width: 6px;
}

.custom-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-sidebar::-webkit-scrollbar-thumb {
  background: rgba(144, 147, 153, 0.3);
  border-radius: 3px;
  transition: background 0.3s;
}

.custom-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(144, 147, 153, 0.5);
}

/* 暗色主题支持 */
.custom-sidebar.dark-theme {
  background: rgba(31, 31, 31, 0.95);
  border-right-color: #414243;
}

.custom-sidebar.dark-theme .sidebar-title {
  color: #e5eaf3;
}

.custom-sidebar.dark-theme .section-title {
  color: #8b949e;
}

.custom-sidebar.dark-theme .group-title {
  color: #8b949e;
}

.custom-sidebar.dark-theme .nav-link {
  color: #c9d1d9;
}

.custom-sidebar.dark-theme .nav-link:hover {
  color: #58a6ff;
  background: rgba(88, 166, 255, 0.1);
}

.custom-sidebar.dark-theme .nav-link.active {
  color: #58a6ff;
  background: rgba(88, 166, 255, 0.15);
}

.custom-sidebar.dark-theme::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.custom-sidebar.dark-theme::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 保留媒体查询作为备用 */
@media (prefers-color-scheme: dark) {
  .custom-sidebar:not(.dark-theme) {
    background: rgba(31, 31, 31, 0.95);
    border-right-color: #414243;
  }
  
  .custom-sidebar:not(.dark-theme) .sidebar-title {
    color: #e5eaf3;
  }
  
  .custom-sidebar:not(.dark-theme) .section-title {
    color: #8b949e;
  }
  
  .custom-sidebar:not(.dark-theme) .nav-link {
    color: #c9d1d9;
  }
  
  .custom-sidebar:not(.dark-theme) .nav-link:hover {
    color: #58a6ff;
    background: rgba(88, 166, 255, 0.1);
  }
  
  .custom-sidebar:not(.dark-theme) .nav-link.active {
    color: #58a6ff;
    background: rgba(88, 166, 255, 0.15);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .custom-sidebar.show {
    transform: translateX(0);
  }
}
</style>