<template>
  <DropdownLogic
    v-bind="$attrs"
    :visible="props.visible"
    :default-visible="props.defaultVisible"
    :trigger="props.trigger"
    :placement="props.placement"
    :offset="props.offset"
    :disabled="props.disabled"
    :close-on-select="props.closeOnSelect"
    :close-on-click-outside="props.closeOnClickOutside"
    :arrow="props.arrow"
    :get-popup-container="props.getPopupContainer"
    :z-index="props.zIndex"
    :aria-label="props.ariaLabel"
    @update:visible="$emit('update:visible', $event)"
    @visible-change="$emit('visibleChange', $event)"
    @select="$emit('select')"
    @click-outside="$emit('clickOutside')"
  >
    <template #trigger>
      <slot name="trigger" />
    </template>

    <slot />
  </DropdownLogic>
</template>

<script setup lang="ts">
  import type { DropdownProps } from './types'
  import DropdownLogic from './DropdownLogic.vue'

  defineOptions({
    name: 'SpDropdown',
  })

  // 用户接口层：定义对外API和属性默认值
  const props = withDefaults(defineProps<DropdownProps>(), {
    trigger: 'click',
    placement: 'bottom-start',
    offset: 4,
    closeOnSelect: true,
    closeOnClickOutside: true,
    arrow: false,
    zIndex: 1000,
    disabled: false,
  })

  // 定义事件，传递给逻辑层
  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    visibleChange: [visible: boolean]
    select: []
    clickOutside: []
  }>()
</script>
