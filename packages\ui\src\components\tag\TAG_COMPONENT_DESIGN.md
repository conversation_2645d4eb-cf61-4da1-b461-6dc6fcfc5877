# Tag 组件设计文档

## 概述

Tag 组件是一个独立的标签组件，用于显示可删除的标签项。它将被 Select 组件的多选模式复用，同时也可以作为独立组件在其他场景中使用。

## 设计理念

### 复用性设计

- 遵循组件库的复用原则，类似于 Select 复用 List 组件
- 提供独立的 Tag 组件，可在多个场景中使用
- 支持不同的变体和配置

### 使用场景

1. **Select 多选模式**：显示选中的选项标签
2. **标签输入**：用户输入的标签列表
3. **分类标签**：内容分类、筛选标签
4. **状态标签**：显示状态信息
5. **可删除列表项**：任何需要可删除项的场景

## 组件架构

### 文件结构

```
packages/ui/src/components/tag/
├── Tag.vue              # 用户接口层
├── TagLogic.vue         # 逻辑处理层
├── TagInner.vue         # DOM渲染层
├── index.ts             # 导出文件
├── types/
│   └── index.ts         # 类型定义
├── constants.ts         # 常量定义
└── README.md           # 组件文档
```

### 三层架构

1. **Tag.vue** - 用户接口层

   - 处理用户 API 和 props 定义
   - 设置默认值
   - 对外暴露方法
   - 事件透传

2. **TagLogic.vue** - 逻辑处理层

   - 状态管理和业务逻辑
   - 事件处理
   - 样式类名计算
   - 上下文提供（如需要）

3. **TagInner.vue** - DOM 渲染层
   - 纯 DOM 元素渲染
   - 最小化职责
   - 无状态组件
   - 无障碍访问支持

### 三层架构实现示例

#### Tag.vue (用户接口层)

```vue
<template>
  <TagLogic
    v-bind="logicProps"
    @close="$emit('close', $event)"
    @click="$emit('click', $event)"
    @check="$emit('check', $event)"
  >
    <slot />
  </TagLogic>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import TagLogic from './TagLogic.vue'
  import type { TagProps, TagEmits } from './types'

  const props = withDefaults(defineProps<TagProps>(), {
    size: 'medium',
    type: 'default',
    variant: 'filled',
    closable: false,
    disabled: false,
    checkable: false,
    checked: false,
    round: false,
    bordered: true,
    truncate: false,
  })

  const emit = defineEmits<TagEmits>()

  const logicProps = computed(() => ({
    ...props,
  }))
</script>
```

#### TagLogic.vue (逻辑处理层)

```vue
<template>
  <TagInner
    :class="tagClasses"
    :style="tagStyles"
    :aria-label="ariaLabel"
    :role="role"
    @click="handleClick"
  >
    <slot />
  </TagInner>
</template>

<script setup lang="ts">
  import { computed } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import TagInner from './TagInner.vue'
  import type { TagProps } from './types'

  const props = defineProps<TagProps>()
  const emit = defineEmits<{
    close: [value: any]
    click: [event: MouseEvent]
    check: [checked: boolean]
  }>()

  const bem = bemHelper('tag')

  const tagClasses = computed(() => [
    bem.b(),
    bem.m(props.size),
    bem.m(props.type),
    bem.m(props.variant),
    {
      [bem.m('disabled')]: props.disabled,
      [bem.m('checked')]: props.checked,
      [bem.m('closable')]: props.closable,
      [bem.m('round')]: props.round,
      [bem.m('bordered')]: props.bordered,
    },
  ])

  const tagStyles = computed(() => {
    const styles: Record<string, any> = {}
    if (props.color) {
      styles['--tag-color'] = props.color
    }
    if (props.maxWidth) {
      styles.maxWidth =
        typeof props.maxWidth === 'number'
          ? `${props.maxWidth}px`
          : props.maxWidth
    }
    return styles
  })

  const ariaLabel = computed(() => {
    return props.label || undefined
  })

  const role = computed(() => {
    return props.checkable ? 'checkbox' : 'button'
  })

  const handleClick = (event: MouseEvent) => {
    if (props.disabled) return
    emit('click', event)

    if (props.checkable) {
      emit('check', !props.checked)
    }
  }

  const handleClose = (event: MouseEvent) => {
    event.stopPropagation()
    if (props.disabled) return
    emit('close', props.value)
  }
</script>
```

#### TagInner.vue (DOM 渲染层)

```vue
<template>
  <span
    :aria-label="ariaLabel"
    :role="role"
    :aria-checked="ariaChecked"
    :aria-disabled="ariaDisabled"
    @click="$emit('click', $event)"
  >
    <slot />
  </span>
</template>

<script setup lang="ts">
  interface TagInnerProps {
    ariaLabel?: string
    role?: string
    ariaChecked?: boolean
    ariaDisabled?: boolean
  }

  defineProps<TagInnerProps>()
  defineEmits<{
    click: [event: MouseEvent]
  }>()
</script>
```

## API 设计

### Props

```typescript
interface TagProps {
  // === 内容相关 ===
  label?: string // 标签文本
  value?: any // 标签值

  // === 外观控制 ===
  size?: 'small' | 'medium' | 'large' // 尺寸
  type?: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info' // 类型，primary会使用主题色
  variant?: 'filled' | 'outlined' | 'light' // 变体
  color?: string // 自定义颜色

  // === 功能控制 ===
  closable?: boolean // 是否可关闭
  disabled?: boolean // 是否禁用
  checkable?: boolean // 是否可选中
  checked?: boolean // 是否选中（checkable为true时）

  // === 图标 ===
  icon?: string // 前置图标
  closeIcon?: string // 自定义关闭图标

  // === 样式 ===
  round?: boolean // 圆角样式
  bordered?: boolean // 是否显示边框

  // === 其他 ===
  maxWidth?: string | number // 最大宽度
  truncate?: boolean // 文本截断
}
```

### Events

```typescript
interface TagEmits {
  (e: 'close', value: any): void // 关闭事件
  (e: 'click', event: MouseEvent): void // 点击事件
  (e: 'check', checked: boolean): void // 选中状态变化（checkable为true时）
}
```

### Slots

```typescript
interface TagSlots {
  default?: () => any // 默认插槽（标签内容）
  icon?: () => any // 图标插槽
  close?: () => any // 关闭按钮插槽
}
```

## 样式设计

### BEM 类名结构

```scss
.sp-tag {
  // 基础样式

  // 尺寸变体
  &--small {
  }
  &--medium {
  }
  &--large {
  }

  // 类型变体
  &--default {
  }
  &--primary {
  }
  &--success {
  }
  &--warning {
  }
  &--error {
  }
  &--info {
  }

  // 样式变体
  &--filled {
  }
  &--outlined {
  }
  &--light {
  }

  // 状态
  &--disabled {
  }
  &--checked {
  }
  &--closable {
  }
  &--round {
  }
  &--bordered {
  }

  // 子元素
  &__content {
  }
  &__icon {
  }
  &__close {
  }
  &__text {
  }
}
```

### 主题集成

- 支持主题色系统
- 响应主题切换
- 自定义颜色支持

## 使用示例

### 1. 基础用法

```vue
<template>
  <!-- 基础标签 -->
  <sp-tag label="标签文本" />

  <!-- 可关闭标签 -->
  <sp-tag
    label="可删除"
    closable
    @close="handleClose"
  />

  <!-- 不同类型 -->
  <sp-tag
    label="默认"
    type="default"
  />
  <sp-tag
    label="主要"
    type="primary"
  />
  <sp-tag
    label="成功"
    type="success"
  />
  <sp-tag
    label="警告"
    type="warning"
  />
  <sp-tag
    label="错误"
    type="error"
  />
</template>
```

### 2. 在 Select 中的使用

```vue
<template>
  <div class="select-tags">
    <sp-tag
      v-for="option in selectedOptions"
      :key="option.value"
      :label="option.label"
      :value="option.value"
      closable
      size="small"
      @close="removeOption"
    />
  </div>
</template>
```

### 3. 可选中标签

```vue
<template>
  <sp-tag
    v-for="tag in tags"
    :key="tag.id"
    :label="tag.name"
    :checked="tag.selected"
    checkable
    @check="handleCheck"
  />
</template>
```

### 4. 自定义样式

```vue
<template>
  <!-- 圆角标签 -->
  <sp-tag
    label="圆角"
    round
  />

  <!-- 自定义颜色 -->
  <sp-tag
    label="自定义"
    color="#ff6b6b"
  />

  <!-- 带图标 -->
  <sp-tag
    label="图标"
    icon="star"
  />

  <!-- 最大宽度限制 -->
  <sp-tag
    label="很长的标签文本内容"
    :max-width="100"
    truncate
  />
</template>
```

## 与 Select 组件的集成

### SelectTag.vue 重构

现有的 `SelectTag.vue` 将基于新的 Tag 组件重构：

```vue
<template>
  <Tag
    :label="label"
    :value="value"
    :size="size"
    :disabled="disabled"
    closable
    variant="outlined"
    @close="$emit('close', value)"
    @click="$emit('click', $event)"
  />
</template>

<script setup lang="ts">
  import Tag from '../tag/Tag.vue'
  // ... 其他逻辑
</script>
```

## 主题样式

### 默认主题

```scss
// packages/theme-default/src/tag.scss
@use 'common/var.scss' as *;

.sp-tag {
  // 基础样式定义
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  height: 24px;
  font-size: 12px;
  line-height: 1;
  border-radius: 4px;
  border: 1px solid transparent;
  cursor: default;

  // 类型样式
  @each $type in (default, primary, success, warning, error, info) {
    &--#{$type} {
      // 每种类型的颜色定义
    }
  }

  // 尺寸样式
  &--small {
    height: 20px;
    padding: 0 6px;
    font-size: 11px;
  }

  &--large {
    height: 28px;
    padding: 0 10px;
    font-size: 13px;
  }
}
```

## 开发计划

### 第一阶段：基础组件

1. 创建基础的 Tag 组件结构
2. 实现基本的显示和关闭功能
3. 添加基础样式和主题支持

### 第二阶段：功能扩展

1. 添加不同类型和变体支持
2. 实现可选中功能
3. 添加图标支持

### 第三阶段：集成优化

1. 重构 SelectTag 组件
2. 在 Select 组件中集成新的 Tag
3. 添加完整的测试和文档

### 第四阶段：高级功能

1. 添加动画效果
2. 支持自定义颜色
3. 添加更多交互功能

## 测试策略

### 单元测试

- 基础渲染测试
- 事件处理测试
- 属性变化测试

### 集成测试

- 与 Select 组件的集成测试
- 主题切换测试
- 响应式测试

### 视觉测试

- 不同状态的视觉回归测试
- 主题适配测试
- 浏览器兼容性测试

## 实现优先级

### 高优先级（必须实现）

1. 基础标签显示
2. 可关闭功能
3. 不同尺寸支持
4. 基础样式和主题集成
5. 与 Select 组件的集成

### 中优先级（重要功能）

1. 不同类型（primary、success 等）
2. 图标支持
3. 禁用状态
4. 文本截断功能

### 低优先级（增强功能）

1. 可选中功能
2. 自定义颜色
3. 动画效果
4. 高级样式变体

## 注意事项

1. **样式一致性**：确保与现有组件的样式风格保持一致
2. **主题适配**：完全支持主题系统，响应主题切换
3. **无障碍性**：添加适当的 ARIA 属性和键盘支持
4. **性能考虑**：在大量标签场景下保持良好性能
5. **向后兼容**：确保不影响现有的 SelectTag 组件功能
