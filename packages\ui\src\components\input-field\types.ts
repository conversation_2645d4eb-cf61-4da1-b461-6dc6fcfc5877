/**
 * InputField 组件类型定义
 * 继承 Input 组件所有功能，并添加表单集成和浮动标签
 */

import type { InputProps, InputEmits } from '../input/types'

/** InputField 专有属性 */
export interface InputFieldOwnProps {
  /** 标签文本 - 支持浮动动画 */
  label?: string
  /** 表单字段名 - 用于表单验证 */
  name: string
  /** 验证规则 */
  rules?: any
  /** 是否必填 */
  required?: boolean
  /** 是否显示验证消息 */
  showMessage?: boolean
  /** 帮助文本 */
  helperText?: string
  /** 标签是否始终浮动（不回落） */
  persistentLabel?: boolean
}

/** InputField 完整属性，继承 Input 的所有属性 */
export interface InputFieldProps
  extends Omit<InputProps, 'validateState' | 'validateMessage'>,
    InputFieldOwnProps {}

/** InputField 事件 */
export interface InputFieldEmits extends InputEmits {
  /** 验证事件 */
  (e: 'validate', name: string, isValid: boolean, message: string): void
}

/** InputField 实例方法 */
export interface InputFieldInstance {
  /** 使输入框获得焦点 */
  focus: () => void
  /** 使输入框失去焦点 */
  blur: () => void
  /** 选中输入框中的文字 */
  select: () => void
  /** 清空输入框 */
  clear: () => void
  /** 验证字段 */
  validate: () => Promise<boolean>
  /** 重置字段 */
  resetField: () => void
  /** 清除验证 */
  clearValidate: () => void
  /** 输入框元素引用 */
  input: HTMLInputElement | null
  /** 包装器元素引用 */
  wrapper: HTMLDivElement | null
}

/** InputField 默认属性 */
export const inputFieldPropsDefaults: Partial<InputFieldProps> = {
  value: '',
  type: 'text',
  variant: 'default',
  effect: 'none',
  size: 'medium',
  disabled: false,
  readonly: false,
  clearable: false,
  showPassword: false,
  showWordLimit: false,
  error: false,
  loading: false,
  required: false,
  showMessage: true,
  persistentLabel: false,
}
