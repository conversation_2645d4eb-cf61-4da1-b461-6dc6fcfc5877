<text>
开关组件支持多种自定义样式组合，可以创建出丰富的视觉效果。
</text>

<template>
    <div class="button-demo-row">
        <span>大尺寸彩色</span>
        <sp-switch
            v-model:value="value1"
            size="huge"
            activeColor="#13ce66"
            inactiveColor="#fff"
            onText="开"
            offText="关"
        />
        <span>方形带文本</span>
        <sp-switch
            v-model:value="value2"
            square size="large"
            onOutText="已启用"
            offOutText="已禁用"
        />
        <span>垂直样式</span>
        <sp-switch 
            v-model:value="value3"
            vertical
            switchOnColor="#409eff"
            buttonOnColor="#ffffff"
        />
    </div>
</template>

<script setup>
    import { ref } from 'vue'

    const value1 = ref(true)
    const value2 = ref(false)
    const value3 = ref(true)
</script>
