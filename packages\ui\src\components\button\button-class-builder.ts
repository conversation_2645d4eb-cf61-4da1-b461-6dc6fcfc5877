/**
 * 按钮类名构建器
 * 提供类型安全的类名生成和逻辑处理
 */

import { computed, type ComputedRef } from 'vue'
import type { BEMHelper } from '@speed-ui/bem-helper'
import type {
  ButtonProps,
  ButtonVariant,
  ButtonColorType,
  ButtonShape,
} from './button'

/**
 * 按钮状态接口
 */
export interface ButtonState {
  isActive: boolean
  isPressing: boolean
  isCountingDown: boolean
}

/**
 * 按钮类名构建器类
 */
export class ButtonClassBuilder {
  private bem: BEMHelper
  private props: ButtonProps
  private state: ButtonState

  constructor(bem: BEMHelper, props: ButtonProps, state: ButtonState) {
    this.bem = bem
    this.props = props
    this.state = state
  }

  /**
   * 获取按钮变体类型（处理互斥逻辑）
   */
  private getVariant(): ButtonVariant {
    // 按优先级返回第一个为 true 的变体
    if (this.props.primary) return 'primary'
    if (this.props.secondary) return 'secondary'
    if (this.props.dashed) return 'dashed'
    if (this.props.text) return 'text'
    if (this.props.link) return 'link'
    return 'secondary' // 默认类型
  }

  /**
   * 获取按钮形状类型（处理互斥逻辑）
   */
  private getShape(): ButtonShape | null {
    // 按优先级返回第一个为 true 的形状
    if (this.props.circle) return 'circle'
    if (this.props.round) return 'round'
    if (this.props.square) return 'square'
    return null
  }

  /**
   * 获取基础类名
   */
  private getBaseClasses(): string[] {
    return [
      this.bem.b(), // sp-button
      this.bem.s('custom'), // sp-button-custom (主题支持)
    ]
  }

  /**
   * 获取变体类名
   */
  private getVariantClasses(): string[] {
    const variant = this.getVariant()
    return [this.bem.m(variant)]
  }

  /**
   * 获取颜色类型类名
   */
  private getColorTypeClasses(): string[] {
    return this.props.type ? [this.bem.m(this.props.type)] : []
  }

  /**
   * 获取尺寸类名
   */
  private getSizeClasses(): string[] {
    return [this.bem.m(this.props.size || 'medium')]
  }

  /**
   * 获取形状类名
   */
  private getShapeClasses(): string[] {
    const shape = this.getShape()
    return shape ? [this.bem.m(shape)] : []
  }

  /**
   * 获取状态类名
   */
  private getStateClasses(): string[] {
    const classes: string[] = []

    if (this.props.disabled) classes.push(this.bem.m('disabled'))
    if (this.props.loading) classes.push(this.bem.m('loading'))
    if (this.state.isActive) classes.push(this.bem.m('active'))
    if (this.state.isPressing) classes.push(this.bem.m('pressing'))
    if (this.state.isCountingDown) classes.push(this.bem.m('countdown'))

    return classes
  }

  /**
   * 获取布局类名
   */
  private getLayoutClasses(): string[] {
    const classes: string[] = []

    if (this.props.vertical) classes.push(this.bem.m('vertical'))

    return classes
  }

  /**
   * 构建完整的类名数组
   */
  build(): string[] {
    return [
      ...this.getBaseClasses(),
      ...this.getVariantClasses(),
      ...this.getColorTypeClasses(),
      ...this.getSizeClasses(),
      ...this.getShapeClasses(),
      ...this.getStateClasses(),
      ...this.getLayoutClasses(),
    ].filter(Boolean)
  }

  /**
   * 获取类名的调试信息
   */
  getDebugInfo(): Record<string, any> {
    return {
      variant: this.getVariant(),
      colorType: this.props.type,
      size: this.props.size || 'medium',
      shape: this.getShape(),
      state: this.state,
      props: {
        disabled: this.props.disabled,
        loading: this.props.loading,
        vertical: this.props.vertical,
      },
    }
  }
}

/**
 * 创建按钮类名构建器的工厂函数
 */
export function createButtonClassBuilder(
  bem: BEMHelper,
  props: ButtonProps,
  state: ButtonState
): ButtonClassBuilder {
  return new ButtonClassBuilder(bem, props, state)
}

/**
 * 创建响应式的按钮类名计算属性
 */
export function useButtonClassName(
  bem: BEMHelper,
  props: ButtonProps,
  state: ComputedRef<ButtonState>
): ComputedRef<string[]> {
  return computed(() => {
    const builder = createButtonClassBuilder(bem, props, state.value)
    return builder.build()
  })
}
