import { computed, type Ref } from 'vue'
import { useBEM } from '@speed-ui/bem-helper'
import type { SelectFieldProps } from './types'

interface SelectFieldLogicState {
  computedDisabled: Ref<boolean>
  isFocused: Ref<boolean>
  hasValue: Ref<boolean>
  isLabelFloating: Ref<boolean>
  validateState: Ref<string>
}

export function useSelectFieldStyles(
  props: SelectFieldProps,
  logicState: SelectFieldLogicState
) {
  const bem = useBEM('select-field')

  // ===== 根容器类名 =====
  const rootClasses = computed(() => [
    bem.b(),
    bem.m(props.size || 'default'),
    bem.m(props.variant || 'default'),
    {
      [bem.m('disabled')]: logicState.computedDisabled.value,
      [bem.m('readonly')]: false, // select-field 不支持 readonly
      [bem.m('focused')]: logicState.isFocused.value,
      [bem.m('error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.m('warning')]: logicState.validateState.value === 'warning',
      [bem.m('success')]: logicState.validateState.value === 'success',
      [bem.m('loading')]: props.loading,
      [bem.m('has-value')]: logicState.hasValue.value,
      [bem.m('label-floating')]: logicState.isLabelFloating.value,
      [bem.m('persistent-label')]: props.persistentLabel,
      [bem.m('multiple')]: props.multiple,
      [bem.m('clearable')]: props.clearable,
    },
  ])

  // ===== 各部分类名 =====
  const wrapperClasses = computed(() => [bem.e('wrapper')])

  const labelClasses = computed(() => [
    bem.e('label'),
    {
      [bem.em('label', 'floating')]: logicState.isLabelFloating.value,
      [bem.em('label', 'required')]: props.required,
    },
  ])

  const suffixClasses = computed(() => [bem.e('suffix')])

  const clearIconClasses = computed(() => [bem.e('clear')])

  const arrowIconClasses = computed(() => [
    bem.e('arrow'),
    {
      [bem.em('arrow', 'reverse')]: logicState.isFocused.value, // 假设下拉时箭头翻转
    },
  ])

  const loadingBarClasses = computed(() => [bem.e('loading-bar')])

  const loadingProgressClasses = computed(() => [bem.e('loading-progress')])

  const helperTextClasses = computed(() => [
    bem.e('helper'),
    {
      [bem.em('helper', 'error')]:
        props.error || logicState.validateState.value === 'error',
      [bem.em('helper', 'warning')]:
        logicState.validateState.value === 'warning',
      [bem.em('helper', 'success')]:
        logicState.validateState.value === 'success',
    },
  ])

  const messageClasses = computed(() => [
    bem.e('message'),
    bem.em('message', logicState.validateState.value || 'error'),
  ])

  return {
    // 类名
    rootClasses,
    wrapperClasses,
    labelClasses,
    suffixClasses,
    clearIconClasses,
    arrowIconClasses,
    loadingBarClasses,
    loadingProgressClasses,
    helperTextClasses,
    messageClasses,
  }
}
