import type { ComponentObjectPropsOptions, PropType } from 'vue'

/**
 * Creates a factory function for props definitions.
 * This is used to define props in a composable then override
 * default values in an implementing component.
 *
 * @example Usage
 * const makeInputProps = propsFactory({
 *   value: String,
 *   disabled: Boolean,
 * }, 'Input')
 *
 * defineComponent({
 *   props: {
 *     ...makeInputProps({
 *       value: '',
 *       disabled: false,
 *     }),
 *   },
 * })
 */
export function propsFactory<
  PropsOptions extends ComponentObjectPropsOptions
>(props: PropsOptions, source?: string) {
  return <Defaults extends PartialKeys<PropsOptions> = {}>(
    defaults?: Defaults
  ): AppendDefault<PropsOptions, Defaults> => {
    return Object.keys(props).reduce<any>((obj, prop) => {
      const isObjectDefinition = typeof props[prop] === 'object' && props[prop] != null && !Array.isArray(props[prop])
      const definition = isObjectDefinition ? props[prop] : { type: props[prop] }

      if (defaults && prop in defaults) {
        obj[prop] = {
          ...definition,
          default: defaults[prop],
        }
      } else {
        obj[prop] = definition
      }

      if (source && !obj[prop].source) {
        obj[prop].source = source
      }

      return obj
    }, {})
  }
}

// Type helpers
type AppendDefault<T extends ComponentObjectPropsOptions, D extends PartialKeys<T>> = {
  [P in keyof T]-?: unknown extends D[P]
    ? T[P]
    : T[P] extends Record<string, unknown>
      ? Omit<T[P], 'type' | 'default'> & {
        type: PropType<MergeTypeDefault<T[P], D[P]>>
        default: MergeDefault<T[P], D[P]>
      }
      : {
        type: PropType<MergeTypeDefault<T[P], D[P]>>
        default: MergeDefault<T[P], D[P]>
      }
}

type MergeTypeDefault<T, D, P = InferPropType<T>> = unknown extends D
  ? P
  : (P | D)

type MergeDefault<T, D, P = InferPropType<T>> = unknown extends D
  ? P
  : (NonNullable<P> | D)

type PartialKeys<T> = { [P in keyof T]?: unknown }

// Copied from Vue's internal types
type InferPropType<T> = [T] extends [null]
  ? any
  : [T] extends [{ type: null | true }]
    ? any
    : [T] extends [ObjectConstructor | { type: ObjectConstructor }]
      ? Record<string, any>
      : [T] extends [BooleanConstructor | { type: BooleanConstructor }]
        ? boolean
        : [T] extends [DateConstructor | { type: DateConstructor }]
          ? Date
          : [T] extends [(infer U)[] | { type: (infer U)[] }]
            ? U extends DateConstructor
              ? Date | InferPropType<U>
              : InferPropType<U>
            : [T] extends [{ type: infer V }]
              ? V
              : T
