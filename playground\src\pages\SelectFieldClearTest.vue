<template>
  <div class="select-field-clear-test">
    <h1>🧪 SelectField 清除按钮测试</h1>
    
    <div class="test-section">
      <h2>单选模式 - 清除按钮测试</h2>
      
      <div class="test-item">
        <h3>基础清除测试</h3>
        <SelectField
          v-model:value="singleValue"
          label="选择城市"
          name="city"
          placeholder="请选择城市"
          :options="cityOptions"
          clearable
        />
        <p class="value-display">当前值: {{ singleValue || '未选择' }}</p>
        <p class="test-instruction">
          💡 测试步骤：
          1. 选择一个城市
          2. 观察是否出现清除按钮（X图标）
          3. 点击清除按钮，值应该被清空
        </p>
      </div>

      <div class="test-item">
        <h3>禁用状态测试</h3>
        <SelectField
          v-model:value="disabledValue"
          label="禁用状态"
          name="disabled"
          placeholder="禁用状态"
          :options="cityOptions"
          :disabled="true"
          clearable
        />
        <p class="value-display">当前值: {{ disabledValue || '未选择' }}</p>
        <p class="test-instruction">
          💡 禁用状态下不应该显示清除按钮
        </p>
      </div>
    </div>

    <div class="test-section">
      <h2>多选模式 - 清除按钮测试</h2>
      
      <div class="test-item">
        <h3>多选清除测试</h3>
        <SelectField
          v-model:value="multipleValue"
          label="选择技能"
          name="skills"
          placeholder="请选择技能"
          :options="skillOptions"
          :multiple="true"
          clearable
        />
        <p class="value-display">当前值: {{ multipleValue.length > 0 ? multipleValue.join(', ') : '未选择' }}</p>
        <p class="test-instruction">
          💡 测试步骤：
          1. 选择多个技能
          2. 观察是否出现清除按钮
          3. 点击清除按钮，所有选择应该被清空
        </p>
      </div>
    </div>

    <div class="test-section">
      <h2>对比测试</h2>
      
      <div class="test-grid">
        <div class="test-item">
          <h3>不可清除</h3>
          <SelectField
            v-model:value="noClearValue"
            label="不可清除"
            name="noClear"
            placeholder="不可清除"
            :options="cityOptions"
            :clearable="false"
          />
          <p class="value-display">当前值: {{ noClearValue || '未选择' }}</p>
          <p class="test-instruction">
            💡 应该只显示下拉箭头，没有清除按钮
          </p>
        </div>

        <div class="test-item">
          <h3>可清除</h3>
          <SelectField
            v-model:value="clearableValue"
            label="可清除"
            name="clearable"
            placeholder="可清除"
            :options="cityOptions"
            clearable
          />
          <p class="value-display">当前值: {{ clearableValue || '未选择' }}</p>
          <p class="test-instruction">
            💡 选择值后应该显示清除按钮和下拉箭头
          </p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>手动测试按钮</h2>
      <div class="button-group">
        <button @click="resetAllValues" class="test-btn">重置所有值</button>
        <button @click="setTestValues" class="test-btn">设置测试值</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { SelectField } from '@speed-ui/ui'

// 测试数据
const singleValue = ref('')
const disabledValue = ref('beijing')
const multipleValue = ref([])
const noClearValue = ref('')
const clearableValue = ref('')

// 选项数据
const cityOptions = [
  { label: '北京', value: 'beijing' },
  { label: '上海', value: 'shanghai' },
  { label: '广州', value: 'guangzhou' },
  { label: '深圳', value: 'shenzhen' },
  { label: '杭州', value: 'hangzhou' },
]

const skillOptions = [
  { label: '前端开发', value: 'frontend' },
  { label: '后端开发', value: 'backend' },
  { label: '移动开发', value: 'mobile' },
  { label: 'UI设计', value: 'ui' },
  { label: '产品设计', value: 'product' },
]

// 测试方法
const resetAllValues = () => {
  singleValue.value = ''
  multipleValue.value = []
  noClearValue.value = ''
  clearableValue.value = ''
}

const setTestValues = () => {
  singleValue.value = 'beijing'
  multipleValue.value = ['frontend', 'backend']
  noClearValue.value = 'shanghai'
  clearableValue.value = 'guangzhou'
}
</script>

<style scoped>
.select-field-clear-test {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
  background: #fafafa;
  min-height: 100vh;
}

h1 {
  color: #1f2937;
  text-align: center;
  margin-bottom: 32px;
  font-size: 2rem;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 12px;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.test-item {
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #f9fafb;
  margin-bottom: 16px;
}

.test-item h3 {
  color: #374151;
  margin-bottom: 12px;
  font-size: 1.1rem;
}

.value-display {
  margin-top: 12px;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  color: #374151;
}

.test-instruction {
  margin-top: 8px;
  padding: 8px 12px;
  background: #eff6ff;
  border-left: 4px solid #3b82f6;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #1e40af;
  line-height: 1.4;
}

.button-group {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.test-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .select-field-clear-test {
    padding: 16px;
  }
  
  .test-grid {
    grid-template-columns: 1fr;
  }
  
  h1 {
    font-size: 1.5rem;
  }
}
</style>
