<!-- 
  搜索输入框组件示例 - 展示如何使用 eventFactory
-->
<template>
  <div class="sp-search-input">
    <InputCore
      v-bind="inputCoreProps"
      @click="handleWrapperClick"
      @focus="handleFocus"
      @blur="handleBlur"
      @input="handleInput"
      @change="handleChange"
      @keydown="handleKeydown"
      @clear="handleClear"
      @prefix-click="handlePrefixClick"
      @suffix-click="handleSuffixClick"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits, ref } from 'vue'
import InputCore from './inputcore'
import { createInputCoreProps } from './inputCoreProps'
import { createSearchEventHandlers } from '../../utils/eventFactory'

// 扩展 InputCore 的 props
const props = defineProps({
  ...createInputCoreProps({
    type: 'search',
    clearable: true,
    prefixIcon: 'search',
    placeholder: '请输入搜索关键词...',
  }),
  
  // 搜索特有的 props
  modelValue: {
    type: String,
    default: '',
  },
  debounceDelay: {
    type: Number,
    default: 300,
  },
})

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: string]
  search: [query: string]
  input: [event: Event]
  change: [event: Event]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  clear: []
  'prefix-click': [event: MouseEvent]
  'suffix-click': [event: MouseEvent]
}>()

// 输入框引用
const inputRef = ref<HTMLInputElement>()

// 计算传递给 InputCore 的 props
const inputCoreProps = computed(() => {
  const { modelValue, debounceDelay, ...coreProps } = props
  return {
    ...coreProps,
    value: modelValue,
    hasValue: Boolean(modelValue),
  }
})

// 使用搜索事件处理器工厂（自动防抖）
const {
  handleWrapperClick,
  handleFocus,
  handleBlur,
  handleInput: baseHandleInput,
  handleChange,
  handleKeydown: baseHandleKeydown,
  handlePrefixClick,
  handleSuffixClick,
  handleClear: baseHandleClear,
} = createSearchEventHandlers(emit, inputRef, props, props.debounceDelay)

// 自定义输入处理（更新 modelValue + 触发搜索）
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  emit('update:modelValue', target.value)
  
  // 触发搜索事件（已经被防抖处理）
  emit('search', target.value)
  
  // 调用基础输入处理器
  baseHandleInput(event)
}

// 自定义键盘事件处理（支持 Enter 搜索）
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    const target = event.target as HTMLInputElement
    emit('search', target.value)
  }
  
  // 调用基础键盘处理器
  baseHandleKeydown(event)
}

// 自定义清除处理
const handleClear = () => {
  emit('update:modelValue', '')
  emit('search', '')
  baseHandleClear()
}

// 暴露方法
defineExpose({
  focus: () => inputRef.value?.focus(),
  blur: () => inputRef.value?.blur(),
  clear: handleClear,
})
</script>

<style scoped>
.sp-search-input {
  /* 搜索输入框特有的样式 */
}
</style>
