import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import Switch from '../switch.vue'

describe('Switch', () => {
  it('renders properly', () => {
    const wrapper = mount(Switch, {
      props: {
        avalue: false
      }
    })
    expect(wrapper.find('.sp-switch').exists()).toBe(true)
  })

  it('emits update:avalue when clicked', async () => {
    const wrapper = mount(Switch, {
      props: {
        avalue: false
      }
    })
    
    await wrapper.find('.sp-switch').trigger('click')
    expect(wrapper.emitted('update:avalue')).toBeTruthy()
    expect(wrapper.emitted('update:avalue')?.[0]).toEqual([true])
  })

  it('does not emit when disabled', async () => {
    const wrapper = mount(Switch, {
      props: {
        avalue: false,
        disabled: true
      }
    })
    
    await wrapper.find('.sp-switch').trigger('click')
    expect(wrapper.emitted('update:avalue')).toBeFalsy()
  })

  it('applies checked class when avalue is true', () => {
    const wrapper = mount(Switch, {
      props: {
        avalue: true
      }
    })
    expect(wrapper.find('.sp-switch--checked').exists()).toBe(true)
  })

  it('applies disabled class when disabled', () => {
    const wrapper = mount(Switch, {
      props: {
        avalue: false,
        disabled: true
      }
    })
    expect(wrapper.find('.sp-switch--disabled').exists()).toBe(true)
  })
})