{"extends": "../../../tsconfig.json", "compilerOptions": {"outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "module": "ESNext", "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "allowImportingTsExtensions": false}, "include": ["src/**/*", "*.ts"], "exclude": ["node_modules", "dist"]}