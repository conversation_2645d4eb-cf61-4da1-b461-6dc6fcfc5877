# Tag 组件

## 概述

Tag 组件是一个可复用的标签组件，采用三层架构设计，支持多种类型、尺寸和交互功能。它可以在 Select 组件的多选模式中使用，也可以作为独立组件在其他场景中使用。

## 特性

- ✅ **三层架构**: Tag.vue (用户接口) → TagLogic.vue (逻辑处理) → TagInner.vue (DOM渲染)
- ✅ **主题支持**: 完全支持主题色系统，`type="primary"` 会使用主题色
- ✅ **多种类型**: default、primary、success、warning、error、info
- ✅ **多种变体**: filled、outlined、light
- ✅ **多种尺寸**: small、medium、large
- ✅ **交互功能**: 可关闭、可选中、禁用状态
- ✅ **自定义颜色**: 支持自定义颜色覆盖
- ✅ **无障碍访问**: 完整的 ARIA 属性和键盘支持
- ✅ **图标支持**: 前置图标和自定义关闭图标

## 基础用法

```vue
<template>
  <!-- 基础标签 -->
  <sp-tag label="标签文本" />
  
  <!-- 可关闭标签 -->
  <sp-tag 
    label="可删除" 
    closable 
    @close="handleClose"
  />
  
  <!-- 不同类型 -->
  <sp-tag label="主要" type="primary" />
  <sp-tag label="成功" type="success" />
  <sp-tag label="警告" type="warning" />
  
  <!-- 不同变体 -->
  <sp-tag label="填充" type="primary" variant="filled" />
  <sp-tag label="轮廓" type="primary" variant="outlined" />
  <sp-tag label="浅色" type="primary" variant="light" />
  
  <!-- 可选中标签 -->
  <sp-tag
    label="可选中"
    :checked="isChecked"
    checkable
    @check="handleCheck"
  />
  
  <!-- 自定义颜色 -->
  <sp-tag label="自定义" color="#ff6b6b" />
</template>
```

## 主题色支持

Tag 组件完全支持主题色系统：

```vue
<template>
  <!-- 这些标签会自动使用当前主题的主色调 -->
  <sp-tag label="主题标签" type="primary" variant="filled" />
  <sp-tag label="主题轮廓" type="primary" variant="outlined" />
  <sp-tag label="主题浅色" type="primary" variant="light" />
</template>

<script setup>
  // 动态切换主题色
  const changeTheme = (color) => {
    document.documentElement.style.setProperty('--sp-color-primary', color)
  }
</script>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| label | string | - | 标签文本 |
| value | any | - | 标签值 |
| size | 'small' \| 'medium' \| 'large' | 'medium' | 尺寸 |
| type | 'default' \| 'primary' \| 'success' \| 'warning' \| 'error' \| 'info' | 'default' | 类型 |
| variant | 'filled' \| 'outlined' \| 'light' | 'filled' | 变体 |
| color | string | - | 自定义颜色 |
| closable | boolean | false | 是否可关闭 |
| disabled | boolean | false | 是否禁用 |
| checkable | boolean | false | 是否可选中 |
| checked | boolean | false | 是否选中 |
| icon | string | - | 前置图标 |
| closeIcon | string | - | 自定义关闭图标 |
| round | boolean | false | 圆角样式 |
| bordered | boolean | true | 是否显示边框 |
| maxWidth | string \| number | - | 最大宽度 |
| truncate | boolean | false | 文本截断 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| close | (value: any) | 关闭事件 |
| click | (event: MouseEvent) | 点击事件 |
| check | (checked: boolean) | 选中状态变化 |

### Slots

| 插槽名 | 说明 |
|--------|------|
| default | 默认插槽（标签内容） |
| icon | 图标插槽 |
| close | 关闭按钮插槽 |

### Methods

| 方法名 | 说明 |
|--------|------|
| focus() | 获得焦点 |
| blur() | 失去焦点 |

## 复用场景

1. **Select 多选模式**: 显示选中的选项标签
2. **标签输入**: 用户输入的标签列表
3. **分类标签**: 内容分类、筛选标签
4. **状态标签**: 显示状态信息
5. **可删除列表项**: 任何需要可删除项的场景

## 架构设计

### 三层架构

1. **Tag.vue** - 用户接口层
   - 处理用户 API 和 props 定义
   - 设置默认值
   - 对外暴露方法
   - 事件透传

2. **TagLogic.vue** - 逻辑处理层
   - 状态管理和业务逻辑
   - 事件处理
   - 样式类名计算
   - 主题色支持

3. **TagInner.vue** - DOM渲染层
   - 纯DOM元素渲染
   - 最小化职责
   - 无状态组件
   - 无障碍访问支持

### 样式系统

- 使用 BEM 命名规范
- 完全支持主题色系统
- 支持自定义颜色覆盖
- 响应式设计

## 演示

访问 [Tag 组件演示页面](http://localhost:5174/zh/tag-demo) 查看完整的功能演示，包括：

- 基础标签
- 类型变体
- 样式变体
- 尺寸变体
- 可选中标签
- 自定义颜色
- 主题切换演示

## 注意事项

1. **主题色**: `type="primary"` 会自动使用当前主题的主色调
2. **复用性**: 组件设计为可在多个场景中复用
3. **无障碍性**: 完整支持键盘导航和屏幕阅读器
4. **性能**: 在大量标签场景下保持良好性能
