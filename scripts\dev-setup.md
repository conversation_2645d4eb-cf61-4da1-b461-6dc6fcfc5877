# Speed UI 开发环境配置指南

## 🎯 **解决 Monorepo 包依赖问题**

### **问题描述**
在 monorepo 中，子包之间的依赖有两种处理方式：
1. **构建后引用** - 需要先打包，然后引用 dist 文件
2. **源码直接引用** - 直接引用源码，开发时无需构建

### **我们的解决方案**

#### **方案 1: Vite 路径别名（推荐用于开发）**
```typescript
// playground/vite.config.ts
export default defineConfig({
  resolve: {
    alias: {
      '@speed-ui/bem-helper': resolve(__dirname, '../packages/common/bem-helper/src'),
      '@speed-ui/config': resolve(__dirname, '../packages/common/config/src'),
      '@speed-ui/hooks': resolve(__dirname, '../packages/hooks/src'),
    }
  }
})
```

**优点**:
- ✅ 开发时无需构建
- ✅ 实时热更新
- ✅ 可以直接调试源码
- ✅ 修改立即生效

#### **方案 2: 包配置源码引用（推荐用于发布）**
```json
// packages/hooks/package.json
{
  "main": "src/index.ts",
  "module": "src/index.ts", 
  "types": "src/index.ts",
  "exports": {
    ".": {
      "development": "./src/index.ts",
      "import": "./lib/index.js",
      "require": "./lib/index.js",
      "types": "./lib/index.d.ts"
    }
  }
}
```

#### **方案 3: 条件导出（最灵活）**
```json
{
  "exports": {
    ".": {
      "development": "./src/index.ts",
      "production": "./lib/index.js",
      "import": "./lib/index.js",
      "require": "./lib/index.js",
      "types": "./lib/index.d.ts"
    }
  }
}
```

## 🚀 **主流框架的做法**

### **Vue 3 生态**
- **开发时**: 直接引用源码
- **发布时**: 构建后的文件
- **工具**: Vite + TypeScript + 路径别名

### **React 生态**
- **开发时**: 使用 Webpack alias
- **发布时**: 独立构建每个包
- **工具**: Webpack + Babel + Lerna

### **Angular 生态**
- **开发时**: ng-packagr + 路径映射
- **发布时**: 构建到 dist
- **工具**: Angular CLI + TypeScript paths

## 💡 **最佳实践建议**

### **开发阶段**
```bash
# 1. 使用路径别名，无需构建
npm run dev  # 直接启动，实时热更新

# 2. 修改公共包代码，立即生效
# 无需 npm run build
```

### **发布阶段**
```bash
# 1. 构建所有包
npm run build:all

# 2. 发布到 npm
npm run publish
```

### **配置文件结构**
```
speed-ui/
├── packages/
│   ├── ui/
│   │   ├── src/           # 源码
│   │   ├── lib/           # 构建后（发布用）
│   │   └── package.json   # 配置条件导出
│   ├── hooks/
│   │   ├── src/           # 源码
│   │   ├── lib/           # 构建后（发布用）
│   │   └── package.json   # 配置条件导出
│   └── common/
│       ├── bem-helper/
│       │   ├── src/       # 源码
│       │   └── lib/       # 构建后（发布用）
│       └── config/
│           ├── src/       # 源码
│           └── lib/       # 构建后（发布用）
├── playground/
│   └── vite.config.ts     # 路径别名配置
└── scripts/
    ├── build.js           # 构建脚本
    └── dev.js             # 开发脚本
```

## 🔧 **具体实施步骤**

### **1. 修改包配置**
```json
// packages/common/bem-helper/package.json
{
  "name": "@speed-ui/bem-helper",
  "main": "src/index.ts",
  "types": "src/index.ts",
  "exports": {
    ".": {
      "development": "./src/index.ts",
      "import": "./lib/index.js",
      "require": "./lib/index.js"
    }
  }
}
```

### **2. 配置构建工具**
```typescript
// vite.config.ts
export default defineConfig({
  resolve: {
    alias: {
      '@speed-ui/bem-helper': resolve(__dirname, '../packages/common/bem-helper/src'),
      '@speed-ui/config': resolve(__dirname, '../packages/common/config/src'),
    }
  }
})
```

### **3. 创建构建脚本**
```json
// package.json
{
  "scripts": {
    "dev": "vite",
    "build": "pnpm -r run build",
    "build:ui": "pnpm --filter @speed-ui/ui run build",
    "build:hooks": "pnpm --filter @speed-ui/hooks run build"
  }
}
```

## 📊 **方案对比**

| 方案 | 开发体验 | 构建复杂度 | 调试难度 | 发布复杂度 | 推荐度 |
|------|----------|------------|----------|------------|--------|
| 构建后引用 | ❌ 差 | ⭐⭐⭐ | ❌ 难 | ✅ 简单 | ⭐⭐ |
| 源码直接引用 | ✅ 好 | ⭐ | ✅ 容易 | ⭐⭐ | ⭐⭐⭐⭐ |
| 路径别名 | ✅ 很好 | ⭐⭐ | ✅ 很容易 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 条件导出 | ✅ 很好 | ⭐⭐⭐ | ✅ 容易 | ✅ 简单 | ⭐⭐⭐⭐⭐ |

## 🎯 **总结**

1. **开发时**: 使用路径别名 + 源码引用，无需构建
2. **发布时**: 构建所有包，使用构建后的文件
3. **最佳实践**: 条件导出 + 路径别名的组合方案
4. **主流做法**: 大部分现代前端项目都采用这种方式

这样既保证了开发体验，又保证了发布质量！🚀
