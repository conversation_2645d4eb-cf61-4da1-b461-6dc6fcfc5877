# Speed UI 主题切换使用示例

## 🎨 基础用法

### 1. 导入主题切换工具

```typescript
import { setTheme, getCurrentTheme, getPresetThemes } from '@speed-ui/theme-default/theme-switcher'
```

### 2. 使用预设主题

```typescript
// 切换到蓝色主题
setTheme('blue')

// 切换到绿色主题
setTheme('green')

// 切换到红色主题
setTheme('red')

// 获取当前主题
const currentTheme = getCurrentTheme()
console.log('当前主题:', currentTheme)

// 获取所有预设主题
const themes = getPresetThemes()
console.log('可用主题:', themes)
// 输出: ['default', 'blue', 'green', 'red', 'orange', 'purple']
```

### 3. 自定义主题

```typescript
// 使用自定义主题色
setTheme({
  primary: '#ff6b6b',
  primaryHover: '#ff5252',
  primaryActive: '#e53935',
  primaryDisabled: '#ffcdd2',
  primaryLightest: '#ffebee',
  primaryLight: '#ffcdd2',
})
```

## 🔧 在 Vue 组件中使用

### 主题切换器组件示例

```vue
<template>
  <div class="theme-switcher">
    <h3>选择主题</h3>
    <div class="theme-options">
      <button
        v-for="theme in themes"
        :key="theme"
        :class="['theme-btn', { active: currentTheme === theme }]"
        @click="switchTheme(theme)"
      >
        {{ themeNames[theme] }}
      </button>
    </div>
    
    <div class="custom-theme">
      <h4>自定义主题色</h4>
      <input
        type="color"
        v-model="customColor"
        @change="applyCustomTheme"
      />
    </div>
    
    <!-- 示例按钮 -->
    <div class="demo-buttons">
      <Button primary>主要按钮</Button>
      <Button secondary>次要按钮</Button>
      <Button dashed>虚线按钮</Button>
      <Button text>文本按钮</Button>
      <Button link>链接按钮</Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { 
  setTheme, 
  getCurrentTheme, 
  getPresetThemes,
  themeSwitcher 
} from '@speed-ui/theme-default/theme-switcher'
import { Button } from '@speed-ui/ui'

const currentTheme = ref(getCurrentTheme())
const themes = ref(getPresetThemes())
const customColor = ref('#667eea')

const themeNames: Record<string, string> = {
  default: '默认紫色',
  blue: '蓝色',
  green: '绿色',
  red: '红色',
  orange: '橙色',
  purple: '紫色',
}

const switchTheme = (theme: string) => {
  setTheme(theme)
  currentTheme.value = theme
}

const applyCustomTheme = () => {
  const customTheme = themeSwitcher.generateThemeFromPrimary(customColor.value)
  setTheme(customTheme)
  currentTheme.value = 'custom'
}

// 监听主题变更
let unsubscribe: (() => void) | null = null

onMounted(() => {
  unsubscribe = themeSwitcher.onThemeChange((theme) => {
    currentTheme.value = theme
    console.log('主题已切换到:', theme)
  })
})

onUnmounted(() => {
  if (unsubscribe) {
    unsubscribe()
  }
})
</script>

<style scoped>
.theme-switcher {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.theme-options {
  display: flex;
  gap: 10px;
  margin: 10px 0;
  flex-wrap: wrap;
}

.theme-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.theme-btn:hover {
  border-color: var(--sp-color-primary);
  color: var(--sp-color-primary);
}

.theme-btn.active {
  background: var(--sp-color-primary);
  border-color: var(--sp-color-primary);
  color: white;
}

.custom-theme {
  margin: 20px 0;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

.demo-buttons {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
</style>
```

## 🌈 主题色预览

### 预设主题色彩

| 主题名称 | 主色调 | 悬停色 | 激活色 |
|---------|--------|--------|--------|
| default | #667eea | #7c8aeb | #5a6ce8 |
| blue    | #1890ff | #40a9ff | #096dd9 |
| green   | #52c41a | #73d13d | #389e0d |
| red     | #ff4d4f | #ff7875 | #d9363e |
| orange  | #fa8c16 | #ffa940 | #d46b08 |
| purple  | #722ed1 | #9254de | #531dab |

## 📱 响应式主题切换

```typescript
// 根据系统主题自动切换
const prefersDark = window.matchMedia('(prefers-color-scheme: dark)')

const updateTheme = () => {
  if (prefersDark.matches) {
    setTheme('purple') // 深色主题使用紫色
  } else {
    setTheme('blue')   // 浅色主题使用蓝色
  }
}

// 监听系统主题变化
prefersDark.addEventListener('change', updateTheme)

// 初始化
updateTheme()
```

## 🎯 最佳实践

### 1. 主题持久化

```typescript
// 保存主题到 localStorage
const saveTheme = (theme: string) => {
  localStorage.setItem('sp-ui-theme', theme)
  setTheme(theme)
}

// 从 localStorage 恢复主题
const restoreTheme = () => {
  const savedTheme = localStorage.getItem('sp-ui-theme')
  if (savedTheme) {
    setTheme(savedTheme)
  }
}

// 应用启动时恢复主题
restoreTheme()
```

### 2. 主题切换动画

```css
/* 为主题切换添加平滑过渡 */
:root {
  transition: 
    --sp-color-primary 0.3s ease,
    --sp-color-primary-hover 0.3s ease,
    --sp-color-primary-active 0.3s ease;
}

/* 所有使用主题变量的元素都会有平滑过渡 */
.sp-button {
  transition: all 0.3s ease;
}
```

### 3. 主题验证

```typescript
// 验证主题色是否有效
const isValidColor = (color: string): boolean => {
  const style = new Option().style
  style.color = color
  return style.color !== ''
}

// 安全的主题设置
const safeSetTheme = (color: string) => {
  if (isValidColor(color)) {
    const customTheme = themeSwitcher.generateThemeFromPrimary(color)
    setTheme(customTheme)
  } else {
    console.warn('无效的颜色值:', color)
  }
}
```

## 🔍 调试主题

```typescript
// 获取当前所有主题变量的值
const getCurrentThemeValues = () => {
  const root = document.documentElement
  const computedStyle = getComputedStyle(root)
  
  return {
    primary: computedStyle.getPropertyValue('--sp-color-primary'),
    primaryHover: computedStyle.getPropertyValue('--sp-color-primary-hover'),
    primaryActive: computedStyle.getPropertyValue('--sp-color-primary-active'),
    primaryDisabled: computedStyle.getPropertyValue('--sp-color-primary-disabled'),
    primaryLightest: computedStyle.getPropertyValue('--sp-color-primary-lightest'),
    primaryLight: computedStyle.getPropertyValue('--sp-color-primary-light'),
  }
}

// 在控制台查看当前主题值
console.log('当前主题变量:', getCurrentThemeValues())
```
