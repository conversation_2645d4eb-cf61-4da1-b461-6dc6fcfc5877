# Speed UI 架构重构说明

## 🎯 **重构目标**

解决之前架构混乱的问题，让项目结构更清晰、职责更明确。

## 📁 **新的项目结构**

```
speed-ui/
├── packages/
│   ├── ui/                    # 核心 UI 组件包
│   │   ├── src/
│   │   │   ├── components/    # 所有组件
│   │   │   │   ├── button/
│   │   │   │   ├── input/
│   │   │   │   ├── config-provider/  # 配置提供者组件
│   │   │   │   └── theme-switcher/   # 主题切换组件
│   │   │   ├── directives/    # 指令
│   │   │   ├── utils/         # 工具函数
│   │   │   └── index.ts       # 主入口
│   │   └── package.json
│   │
│   ├── hooks/                 # 🆕 Vue 组合式函数包
│   │   ├── src/
│   │   │   ├── use-theme/     # 主题相关 hooks
│   │   │   └── index.ts
│   │   └── package.json
│   │
│   ├── theme-default/         # 默认主题包
│   │   ├── src/
│   │   │   ├── button.scss
│   │   │   ├── common/
│   │   │   └── index.scss
│   │   └── package.json
│   │
│   ├── bem-helper/            # BEM 辅助工具包
│   └── utils/                 # 通用工具包
│
├── playground/                # 开发测试环境
├── docs/                      # 文档
└── examples/                  # 使用示例
```

## 🔧 **各包的职责**

### **@speed-ui/ui** - 核心组件包
- **职责**: 提供所有 Vue 组件
- **包含**: Button、Input、ConfigProvider、ThemeSwitcher 等
- **不包含**: 组合式函数（hooks）

### **@speed-ui/hooks** - 组合式函数包
- **职责**: 提供 Vue 3 组合式函数
- **包含**: useTheme、useForms、useTable 等
- **优势**: 独立发布、按需引入、逻辑复用

### **@speed-ui/theme-default** - 主题包
- **职责**: 提供默认样式和主题
- **包含**: SCSS 文件、CSS 变量、主题切换工具

### **@speed-ui/bem-helper** - BEM 工具包
- **职责**: 提供 BEM 命名规范工具
- **包含**: 类名生成器、命名规范

## 🚀 **用户使用方式**

### **方式 1: 完整安装（推荐）**
```bash
npm install @speed-ui/ui @speed-ui/theme-default
```

```typescript
// main.ts
import { createApp } from 'vue'
import SpeedUI from '@speed-ui/ui'
import '@speed-ui/theme-default/lib/index.css'

const app = createApp(App)
app.use(SpeedUI)
app.mount('#app')
```

### **方式 2: 按需使用 hooks**
```bash
npm install @speed-ui/ui @speed-ui/hooks @speed-ui/theme-default
```

```vue
<template>
  <div>
    <SpButton primary>按钮</SpButton>
    <button @click="changeTheme">切换主题</button>
  </div>
</template>

<script setup>
import { SpButton } from '@speed-ui/ui'
import { useTheme } from '@speed-ui/hooks'

const { setTheme } = useTheme()

const changeTheme = () => {
  setTheme('blue')
}
</script>
```

### **方式 3: 使用 ConfigProvider**
```vue
<template>
  <SpConfigProvider :theme="{ primaryColor: '#1890ff' }">
    <App />
  </SpConfigProvider>
</template>

<script setup>
import { SpConfigProvider } from '@speed-ui/ui'
</script>
```

### **方式 4: 使用主题切换组件**
```vue
<template>
  <SpThemeSwitcher 
    :show-presets="true" 
    :show-custom="true" 
    @theme-change="handleThemeChange" 
  />
</template>

<script setup>
import { SpThemeSwitcher } from '@speed-ui/ui'

const handleThemeChange = (theme) => {
  console.log('主题已切换:', theme)
}
</script>
```

## 📦 **包依赖关系**

```
@speed-ui/ui
├── @speed-ui/bem-helper
├── @speed-ui/utils
└── vue (peer)

@speed-ui/hooks
├── @speed-ui/utils
└── vue (peer)

@speed-ui/theme-default
└── sass (dev)
```

## 🎯 **ConfigProvider 的作用**

### **什么是 ConfigProvider？**
ConfigProvider 是一个**配置提供者组件**，用于为其子组件提供全局配置。

### **为什么需要 ConfigProvider？**
1. **全局配置**: 一次配置，所有子组件都生效
2. **局部覆盖**: 可以为特定区域设置不同的主题
3. **类型安全**: 提供 TypeScript 类型支持
4. **标准化**: 参考 Ant Design、Naive UI 等主流库的做法

### **ConfigProvider 的位置**
- **位置**: `packages/ui/src/components/config-provider/`
- **原因**: 它是一个 Vue 组件，应该和其他组件放在一起
- **作用**: 接收配置 props，应用到 CSS 变量，提供给子组件

### **使用示例**
```vue
<!-- 全局配置 -->
<SpConfigProvider :theme="{ primaryColor: '#1890ff' }">
  <App />
</SpConfigProvider>

<!-- 局部配置 -->
<div>
  <SpButton primary>全局主题按钮</SpButton>
  
  <SpConfigProvider :theme="{ primaryColor: '#ff4d4f' }">
    <SpButton primary>红色主题按钮</SpButton>
  </SpConfigProvider>
</div>
```

## 🔄 **迁移指南**

### **从旧版本迁移**
```typescript
// 旧版本
import { useTheme } from '@speed-ui/ui'

// 新版本
import { useTheme } from '@speed-ui/hooks'
```

### **向后兼容**
为了保持向后兼容，UI 包仍然导出 `SpConfigProvider` 别名：
```typescript
// 两种方式都可以
import { ConfigProvider } from '@speed-ui/ui'
import { SpConfigProvider } from '@speed-ui/ui'
```

## 💡 **优势总结**

### **1. 职责清晰**
- UI 组件 → `@speed-ui/ui`
- 组合式函数 → `@speed-ui/hooks`
- 样式主题 → `@speed-ui/theme-default`

### **2. 按需引入**
- 只需要组件？安装 `@speed-ui/ui`
- 需要 hooks？额外安装 `@speed-ui/hooks`
- 自定义主题？使用 `@speed-ui/theme-default`

### **3. 独立发布**
- 每个包可以独立版本管理
- hooks 包可以在其他项目中复用
- 主题包可以有多个变体

### **4. 更好的开发体验**
- 类型安全的主题配置
- 清晰的文件结构
- 标准化的使用方式

这样的架构更加清晰、可维护，也更符合现代前端项目的最佳实践！🎉
