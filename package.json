{"name": "speed-ui", "version": "1.0.0", "description": "Vue3 组件库 Monorepo 项目", "private": true, "workspaces": ["packages/*", "playground", "docs"], "scripts": {"dev": "pnpm -C playground dev", "docs:dev": "pnpm -C docs dev", "docs:build": "pnpm -C docs build", "docs:preview": "pnpm -C docs preview", "build": "pnpm -r --filter=!playground --filter=!docs build", "build:ui": "pnpm -C packages/ui build", "build:cases": "node build/case-processor.js", "watch:cases": "node build/watch-cases.js", "sync:cases": "node build/sync-cases.cjs", "test": "pnpm -r test", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "format:button": "prettier --write packages/ui/src/components/button/button.vue", "typecheck": "vue-tsc --noEmit", "clean": "pnpm -r clean", "changeset": "changeset", "version-packages": "changeset version", "release": "pnpm build && changeset publish"}, "dependencies": {"@floating-ui/vue": "^1.1.6", "@vicons/antd": "^0.13.0", "@vicons/ionicons5": "^0.13.0", "vue": "^3.4.0"}, "devDependencies": {"@changesets/cli": "^2.26.2", "@testing-library/vue": "^8.1.0", "@types/jsdom": "^21.1.7", "@types/node": "^20.5.0", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@vitejs/plugin-vue": "^5.2.4", "@vue/test-utils": "^2.4.6", "chokidar": "^3.5.3", "eslint": "^8.50.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^26.1.0", "marked": "^9.1.6", "rimraf": "^5.0.1", "sass": "^1.66.1", "shiki": "^3.4.2", "typescript": "^5.2.2", "vite": "^6.3.5", "vite-plugin-dts": "^3.5.3", "vitest": "^3.2.1", "vue-tsc": "^1.8.15"}, "packageManager": "pnpm@8.7.0"}