/**
 * Speed UI BEM 助手类型定义
 * 提供完整的 TypeScript 类型支持
 */

/**
 * BEM 助手接口
 * 提供生成 BEM 规范类名的方法
 */
export interface BEMHelper {
  /**
   * 生成基础块类名
   * @returns 块类名 (如: sp-button)
   */
  b(): string
  
  /**
   * 生成元素类名
   * @param element 元素名
   * @returns 元素类名 (如: sp-button__icon)
   */
  e<E extends string>(element: E): string
  
  /**
   * 生成修饰符类名
   * @param modifier 修饰符名
   * @returns 修饰符类名 (如: sp-button--primary)
   */
  m<M extends string>(modifier: M): string
  
  /**
   * 生成元素修饰符类名
   * @param element 元素名
   * @param modifier 修饰符名
   * @returns 元素修饰符类名 (如: sp-button__icon--large)
   */
  em<E extends string, M extends string>(element: E, modifier: M): string
  
  /**
   * 生成状态类名（用于组合组件）
   * @param state 状态名
   * @returns 状态类名 (如: sp-button-group)
   */
  s<S extends string>(state: S): string
  
  /**
   * 生成 CSS 选择器
   * @returns CSS 选择器 (如: .sp-button)
   */
  selector(): string
  
  /**
   * 生成元素 CSS 选择器
   * @param element 元素名
   * @returns 元素 CSS 选择器 (如: .sp-button__icon)
   */
  selectorE<E extends string>(element: E): string
  
  /**
   * 生成修饰符 CSS 选择器
   * @param modifier 修饰符名
   * @returns 修饰符 CSS 选择器 (如: .sp-button--primary)
   */
  selectorM<M extends string>(modifier: M): string
  
  /**
   * 生成 CSS 变量名
   * @param variable 变量名
   * @returns CSS 变量名 (如: --sp-button-color)
   */
  var<V extends string>(variable: V): string
  
  /**
   * 生成 CSS 变量值
   * @param variable 变量名
   * @returns CSS 变量值 (如: var(--sp-button-color))
   */
  getVar<V extends string>(variable: V): string
  
  /**
   * 生成 CSS 变量声明
   * @param variable 变量名
   * @param value 变量值
   * @returns CSS 变量声明 (如: --sp-button-color: #fff;)
   */
  setVar<V extends string, S extends string>(variable: V, value: S): string
  
  /**
   * 批量生成 CSS 变量对象
   * @param variables 变量对象
   * @returns CSS 变量对象
   */
  vars<T extends Record<string, any>>(variables: T): Record<string, any>
  
  /**
   * 生成全局 CSS 变量名（仅使用命名空间）
   * @param variable 变量名
   * @returns 全局 CSS 变量名 (如: --sp-primary-color)
   */
  globalVar<V extends string>(variable: V): string
  
  /**
   * 生成全局 CSS 变量值
   * @param variable 变量名
   * @returns 全局 CSS 变量值 (如: var(--sp-primary-color))
   */
  getGlobalVar<V extends string>(variable: V): string
}

/**
 * CSS 变量映射类型
 * 用于类型安全的 CSS 变量对象
 */
export type CSSVarMap<T extends Record<string, any>, Prefix extends string> = {
  [K in keyof T as `--${Prefix}-${string & K}`]: T[K]
}

/**
 * BEM 配置选项
 */
export interface BEMOptions {
  /** 命名空间前缀，默认为 'sp' */
  namespace?: string
  /** 是否启用严格模式（类型检查更严格） */
  strict?: boolean
}

/**
 * 常用的 CSS 变量类型
 */
export interface CommonCSSVars {
  /** 主色调 */
  'primary-color'?: string
  /** 次要色调 */
  'secondary-color'?: string
  /** 成功色调 */
  'success-color'?: string
  /** 警告色调 */
  'warning-color'?: string
  /** 错误色调 */
  'error-color'?: string
  /** 文字颜色 */
  'text-color'?: string
  /** 背景颜色 */
  'bg-color'?: string
  /** 边框颜色 */
  'border-color'?: string
  /** 阴影 */
  'box-shadow'?: string
  /** 圆角 */
  'border-radius'?: string
  /** 字体大小 */
  'font-size'?: string
  /** 行高 */
  'line-height'?: string
  /** 间距 */
  'spacing'?: string
}

/**
 * 组件尺寸类型
 */
export type ComponentSize = 'small' | 'medium' | 'large'

/**
 * 组件状态类型
 */
export type ComponentState = 'default' | 'hover' | 'active' | 'disabled' | 'focus'

/**
 * 组件类型
 */
export type ComponentType = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'

/**
 * 导出所有类型
 */
export type {
  BEMHelper as default
}
