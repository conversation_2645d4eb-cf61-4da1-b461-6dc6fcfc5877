<template>
  <transition name="sp-select-dropdown">
    <div
      v-show="visible"
      :class="[
        `${prefixCls}__dropdown`,
        {
          [`${prefixCls}__dropdown--${validateState}`]: validateState,
          [`${prefixCls}__dropdown--${variant}`]: variant,
        },
      ]"
      :style="dropdownStyle"
    >
      <Scrollbar
        max-height="200px"
        :size="scrollbarSize"
      >
        <List
          :selectable="true"
          :multiple="multiple"
          :selected-keys="getSelectedKeys()"
          :hoverable="true"
          :variant="variant"
          @update:selected-keys="handleSelectedKeysChange"
        >
          <!-- 选项列表 -->
          <ListItem
            v-for="option in options"
            :key="getOptionValue(option, valueKey)"
            :item-key="String(getOptionValue(option, valueKey))"
            :title="getOptionLabel(option, labelKey)"
            :disabled="isOptionDisabled(option)"
            :selectable="false"
            :simple="true"
            :class="getOptionClassName(option)"
            @click="(_key, event) => handleOptionClick(option, event)"
          >
            <template #suffix>
              <i
                v-if="isSelected(option, value, multiple, valueKey)"
                :class="`${prefixCls}__option-check`"
              >
                <Checkmark />
              </i>
            </template>
          </ListItem>

          <!-- 无选项提示 -->
          <div
            v-if="options.length === 0"
            :class="`${prefixCls}__empty`"
          >
            {{ emptyText }}
          </div>
        </List>
      </Scrollbar>
    </div>
  </transition>
</template>

<script setup lang="ts">
  import type { SelectOption } from '../select'
  import {
    getOptionValue,
    getOptionLabel,
    isSelected,
    isOptionDisabled,
  } from '../select'
  import { List, ListItem } from '../../list'
  import Scrollbar from '../../scrollbar'
  import { classNames } from '../../../utils'
  import { Checkmark } from '@vicons/ionicons5'
  import { computed } from 'vue'
  import type { ComponentSize } from '@speed-ui/size-helper'
  import { mapSizeToScrollbar } from '@speed-ui/size-helper'

  interface Props {
    visible: boolean
    options: SelectOption[]
    prefixCls: string
    dropdownStyle: Record<string, any>
    value?: string | number | Array<string | number>
    multiple: boolean
    valueKey: string
    labelKey?: string
    emptyText: string
    variant?: 'default' | 'simple'
    size?: ComponentSize
    validateState?: 'success' | 'warning' | 'error'
  }

  interface Emits {
    optionClick: [option: SelectOption]
  }

  const props = defineProps<Props>()
  const emit = defineEmits<Emits>()

  // 将 Select 的 size 映射到 Scrollbar 的 size
  const scrollbarSize = computed(() => {
    return mapSizeToScrollbar(props.size || 'default')
  })

  // 获取选中的 keys（用于 List 组件）
  const getSelectedKeys = () => {
    if (props.multiple) {
      const values = Array.isArray(props.value) ? props.value : []
      return values.map(v => String(v))
    } else {
      return props.value !== undefined && props.value !== null
        ? [String(props.value)]
        : []
    }
  }

  // 获取选项类名
  const getOptionClassName = (option: SelectOption) => {
    return classNames(`${props.prefixCls}__option`, {
      [`${props.prefixCls}__option--selected`]: isSelected(
        option,
        props.value,
        props.multiple,
        props.valueKey
      ),
      [`${props.prefixCls}__option--disabled`]: isOptionDisabled(option),
    })
  }

  // 处理 List 组件的选择事件
  const handleSelectedKeysChange = (_keys: string[]) => {
    // 这里我们不直接处理，因为选择逻辑由 SelectOption 的点击事件处理
    // 这个函数主要是为了满足 List 组件的 API 要求
  }

  // 处理选项点击
  const handleOptionClick = (option: SelectOption, event?: MouseEvent) => {
    // 阻止事件冒泡，防止触发外部点击处理
    if (event) {
      event.stopPropagation()
    }
    emit('optionClick', option)
  }
</script>

<script lang="ts">
  export default {
    name: 'SelectDropdown',
  }
</script>
