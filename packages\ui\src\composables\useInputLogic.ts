import { ref, computed, nextTick } from 'vue'
import type { InputProps, InputEmits } from '../types'

export function useInputLogic(props: InputProps, emit: InputEmits) {
  // ===== 组件引用 =====
  const wrapperRef = ref<HTMLDivElement>()
  const inputRef = ref<HTMLInputElement>()

  // ===== 状态管理 =====
  const passwordVisible = ref(false)
  const isFocused = ref(false)

  // ===== 计算属性 =====
  const actualType = computed(() => {
    if (props.type === 'password') {
      return passwordVisible.value ? 'text' : 'password'
    }
    return props.type
  })

  const iconSize = computed(() => {
    switch (props.size) {
      case 'small':
        return 12
      case 'large':
        return 18
      case 'medium':
      default:
        return 14
    }
  })

  const hasValue = computed(() => {
    return !!props.value && String(props.value).length > 0
  })

  const wordCount = computed(() => {
    return String(props.value || '').length
  })

  const computedDisabled = computed(() => {
    return props.disabled || props.loading
  })

  const showClearIcon = computed(() => {
    return (
      props.clearable &&
      !props.disabled &&
      !props.readonly &&
      !props.loading &&
      hasValue.value
    )
  })

  const showPasswordIcon = computed(() => {
    return (
      props.showPassword &&
      props.type === 'password' &&
      !props.disabled &&
      !props.readonly &&
      !props.loading
    )
  })

  const passwordIconName = computed(() => {
    return passwordVisible.value ? 'EyeOff' : 'Eye'
  })

  const showSuffix = computed(() => {
    return (
      showClearIcon.value ||
      showPasswordIcon.value ||
      props.showWordLimit ||
      props.suffixIcon
    )
  })

  // ===== 事件处理 =====
  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = target.value
    emit('update:value', value === '' ? undefined : value)
    emit('input', event)
  }

  const handleChange = (event: Event) => {
    const target = event.target as HTMLInputElement
    const value = target.value
    emit('change', value === '' ? undefined : value)
  }

  const handleFocus = (event: FocusEvent) => {
    isFocused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    isFocused.value = false
    emit('blur', event)
  }

  const handleKeydown = (event: KeyboardEvent) => {
    // 处理特殊按键
    if (event.key === 'Enter') {
      // 回车键处理
    }
  }

  const handleWrapperClick = () => {
    if (!props.disabled && !props.readonly && !props.loading) {
      focus()
    }
  }

  const handleClear = () => {
    emit('update:value', undefined)
    emit('clear')
    nextTick(() => {
      focus()
    })
  }

  const togglePassword = () => {
    passwordVisible.value = !passwordVisible.value
    nextTick(() => {
      focus()
    })
  }

  // ===== 暴露的方法 =====
  const focus = () => {
    inputRef.value?.focus()
  }

  const blur = () => {
    inputRef.value?.blur()
  }

  const select = () => {
    inputRef.value?.select()
  }

  const clear = () => {
    emit('update:value', undefined)
    emit('clear')
  }

  // ===== 返回所有状态和方法 =====
  return {
    // 引用
    wrapperRef,
    inputRef,

    // 状态
    passwordVisible,
    isFocused,

    // 计算属性
    actualType,
    iconSize,
    hasValue,
    wordCount,
    computedDisabled,

    // 显示逻辑
    showClearIcon,
    showPasswordIcon,
    passwordIconName,
    showSuffix,

    // 事件处理
    handleInput,
    handleChange,
    handleFocus,
    handleBlur,
    handleKeydown,
    handleWrapperClick,
    handleClear,
    togglePassword,

    // 方法
    focus,
    blur,
    select,
    clear,
  }
}
