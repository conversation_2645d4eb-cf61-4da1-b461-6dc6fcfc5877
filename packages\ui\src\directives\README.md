# v-linkage 指令

`v-linkage` 是 Speed UI 提供的组件联动指令，用于简化组件间的状态同步和属性联动。

## 基本用法

### 简单联动

```vue
<template>
  <!-- 当输入框的值改变时，按钮的disabled状态会自动更新 -->
  <sp-input 
    v-model="username" 
    v-linkage="{ target: 'submitBtn', when: 'value', then: 'disabled' }"
    placeholder="请输入用户名"
  />
  
  <sp-button ref="submitBtn">提交</sp-button>
</template>
```

### 条件联动

```vue
<template>
  <!-- 只有当输入框为空时，按钮才被禁用 -->
  <sp-input 
    v-model="username" 
    v-linkage="{
      target: 'submitBtn',
      when: 'value',
      then: 'disabled',
      condition: (value) => !value || value.trim() === ''
    }"
  />
  
  <sp-button ref="submitBtn">提交</sp-button>
</template>
```

### 值转换

```vue
<template>
  <!-- 将输入框的值转换为大写后显示在另一个输入框中 -->
  <sp-input 
    v-model="input1" 
    v-linkage="{
      target: 'input2',
      when: 'value',
      then: 'value',
      transform: (value) => value?.toUpperCase()
    }"
    placeholder="输入小写字母"
  />
  
  <sp-input ref="input2" placeholder="自动转换为大写" readonly />
</template>
```

### 多目标联动

```vue
<template>
  <!-- 一个开关控制多个组件的状态 -->
  <sp-switch 
    v-model="isEnabled" 
    v-linkage="{
      target: ['input1', 'input2', 'submitBtn'],
      when: 'value',
      then: 'disabled',
      transform: (value) => !value
    }"
  />
  
  <sp-input ref="input1" />
  <sp-input ref="input2" />
  <sp-button ref="submitBtn">提交</sp-button>
</template>
```

### 复杂动作

```vue
<template>
  <!-- 使用动作对象进行更复杂的控制 -->
  <sp-input 
    v-model="password" 
    v-linkage="{
      target: 'strengthIndicator',
      when: 'value',
      then: {
        prop: 'type',
        transform: (value) => {
          if (!value) return 'info'
          if (value.length < 6) return 'danger'
          if (value.length < 10) return 'warning'
          return 'success'
        }
      }
    }"
    type="password"
    placeholder="请输入密码"
  />
  
  <sp-button ref="strengthIndicator" size="small">密码强度</sp-button>
</template>
```

### 多重联动

```vue
<template>
  <!-- 一个组件可以设置多个联动规则 -->
  <sp-input 
    v-model="email" 
    v-linkage="[
      {
        target: 'submitBtn',
        when: 'value',
        then: 'disabled',
        condition: (value) => !value?.includes('@')
      },
      {
        target: 'emailTip',
        when: 'value',
        then: 'hidden',
        condition: (value) => value?.includes('@')
      }
    ]"
    placeholder="请输入邮箱"
  />
  
  <div ref="emailTip" class="tip">请输入有效的邮箱地址</div>
  <sp-button ref="submitBtn">提交</sp-button>
</template>
```

### 延迟联动

```vue
<template>
  <!-- 延迟500ms后执行联动，避免频繁更新 -->
  <sp-input 
    v-model="searchText" 
    v-linkage="{
      target: 'searchBtn',
      when: 'value',
      then: 'loading',
      delay: 500,
      transform: (value) => value?.length > 2
    }"
    placeholder="搜索..."
  />
  
  <sp-button ref="searchBtn">搜索</sp-button>
</template>
```

## API

### LinkageConfig

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| target | `string \| string[]` | ✅ | - | 目标组件的ref名称 |
| when | `string` | ✅ | - | 监听的源属性名，支持嵌套属性 |
| then | `string \| LinkageAction` | ✅ | - | 目标属性名或动作对象 |
| condition | `(value: any) => boolean` | ❌ | - | 自定义条件函数 |
| transform | `(value: any) => any` | ❌ | - | 值转换函数 |
| delay | `number` | ❌ | 0 | 延迟执行时间(毫秒) |

### LinkageAction

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| prop | `string` | ✅ | - | 目标属性名 |
| value | `any` | ❌ | - | 固定值，设置后忽略源值 |
| transform | `(value: any) => any` | ❌ | - | 值转换函数 |

## 注意事项

1. **ref 引用**：目标组件必须设置 `ref` 属性
2. **组件实例**：指令会自动检测目标是Vue组件还是DOM元素
3. **性能优化**：使用 `delay` 属性可以避免频繁的联动更新
4. **嵌套属性**：支持 `user.profile.name` 这样的嵌套属性访问
5. **类型安全**：配合 TypeScript 使用时会有完整的类型提示

## 常见用例

- 表单验证联动
- 按钮状态控制
- 组件显示/隐藏
- 数据同步
- 级联选择
- 条件渲染