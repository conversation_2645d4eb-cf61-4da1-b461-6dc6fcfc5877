{"name": "@speed-ui/bem-helper", "version": "1.0.0", "description": "Speed UI BEM 类名助手工具 - 用于生成符合 BEM 规范的 CSS 类名", "license": "MIT", "author": "Speed UI Team", "keywords": ["speed-ui", "bem", "css", "classname", "helper", "utility", "typescript"], "type": "module", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.cjs.js"}}, "files": ["dist", "src", "README.md"], "scripts": {"build": "vite build", "dev": "vite build --watch", "prepublishOnly": "npm run build"}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/your-org/speed-ui.git", "directory": "packages/common/bem-helper"}, "homepage": "https://speed-ui.dev", "bugs": {"url": "https://github.com/your-org/speed-ui/issues"}}