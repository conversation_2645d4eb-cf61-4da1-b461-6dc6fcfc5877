<template>
  <DropdownInner
    ref="dropdownInnerRef"
    :dropdown-classes="dropdownClasses"
    :dropdown-styles="dropdownStyles"
    :content-classes="contentClasses"
    :content-styles="contentStyles"
    :arrow-styles="arrowStyles"
    :trigger-props="triggerProps"
    :visible="visible"
    :arrow="props.arrow"
    :aria-label="props.ariaLabel"
    popup-container="body"
    @content-click="handleContentClick"
    @content-keydown="handleContentKeydown"
  >
    <template #trigger>
      <slot name="trigger" />
    </template>

    <slot />
  </DropdownInner>
</template>

<script setup lang="ts">
  import {
    computed,
    provide,
    ref,
    watch,
    nextTick,
    onMounted,
    onUnmounted,
  } from 'vue'
  import {
    useFloating,
    autoUpdate,
    offset,
    flip,
    shift,
  } from '@floating-ui/vue'
  import { dropdownKey } from './constants'
  import { type DropdownProps } from './types'
  import { createBEM } from '@speed-ui/bem-helper'
  import DropdownInner from './DropdownInner.vue'

  defineOptions({
    name: 'SpDropdownLogic',
  })

  // Props
  const props = withDefaults(defineProps<DropdownProps>(), {
    visible: undefined,
    defaultVisible: false,
    trigger: 'click',
    placement: 'bottom',
    arrow: false,
    disabled: false,
    closeOnSelect: true,
    closeOnClickOutside: true,
    zIndex: 1050,
    offset: 4,
  })

  // Emits
  const emit = defineEmits<{
    'update:visible': [visible: boolean]
    visibleChange: [visible: boolean]
  }>()

  // 创建BEM工具
  const bem = createBEM('dropdown')

  // 内部状态
  const innerVisible = ref(props.defaultVisible || false)
  const dropdownInnerRef = ref<InstanceType<typeof DropdownInner>>()

  // 使用 floating-ui 进行定位
  const { floatingStyles, placement: computedPlacement } = useFloating(
    computed(() => {
      const triggerEl = dropdownInnerRef.value?.triggerRef
      console.log('🎯 Floating UI - triggerRef:', triggerEl)
      return triggerEl
    }),
    computed(() => {
      const contentEl = dropdownInnerRef.value?.contentRef
      console.log('🎯 Floating UI - contentRef:', contentEl)
      return contentEl
    }),
    {
      placement: computed(() => props.placement),
      middleware: [offset(props.offset), flip(), shift({ padding: 8 })],
      whileElementsMounted: autoUpdate,
    }
  )

  // 计算属性
  const visible = computed(() => {
    // 优先使用外部传入的 visible，没有则使用内部状态
    const result =
      props.visible !== undefined ? props.visible : innerVisible.value
    console.log(
      '🔍 计算 visible - props.visible:',
      props.visible,
      'innerVisible:',
      innerVisible.value,
      'result:',
      result
    )
    return result
  })

  // 监听外部 visible 变化，同步到内部状态
  watch(
    () => props.visible,
    newVisible => {
      if (newVisible !== undefined && newVisible !== innerVisible.value) {
        innerVisible.value = newVisible
        console.log('🔄 Syncing external visible to internal:', newVisible)
      }
    }
  )

  // 监听visible变化，确保DOM引用就绪
  watch(visible, async newVisible => {
    if (newVisible) {
      // 等待DOM更新
      await nextTick()
      console.log('👀 Visible changed to true, DOM refs:', {
        trigger: dropdownInnerRef.value?.triggerRef,
        content: dropdownInnerRef.value?.contentRef,
      })
    }
  })

  const dropdownClasses = computed(() => [
    'sp-dropdown',
    { 'sp-dropdown--disabled': props.disabled },
  ])

  const dropdownStyles = computed(() => ({}))

  const contentClasses = computed(() => [
    'sp-dropdown__overlay',
    `sp-dropdown__overlay--${computedPlacement.value}`,
  ])

  const contentStyles = computed(() => {
    const styles = {
      zIndex: props.zIndex,
      ...floatingStyles.value,
    }
    console.log('🎨 Floating Styles:', floatingStyles.value)
    console.log('🎨 Final Content Styles:', styles)
    return styles
  })

  const arrowStyles = computed(() => ({}))

  // 触发器属性
  const triggerProps = computed(() => {
    const triggerEvents: Record<string, any> = {}

    if (props.trigger === 'click') {
      triggerEvents.onClick = handleTriggerClick
    } else if (props.trigger === 'hover') {
      triggerEvents.onMouseenter = handleTriggerMouseenter
      triggerEvents.onMouseleave = handleTriggerMouseleave
    }

    return triggerEvents
  })

  // 处理函数
  const handleTriggerClick = () => {
    console.log('🖱️ 点击触发器')
    if (props.disabled) return

    const newVisible = !visible.value
    console.log('📋 新的visible状态:', newVisible)

    innerVisible.value = newVisible
    emit('update:visible', newVisible)
    emit('visibleChange', newVisible)
  }

  const handleTriggerMouseenter = () => {
    if (props.disabled) return
    innerVisible.value = true
    emit('update:visible', true)
    emit('visibleChange', true)
  }

  const handleTriggerMouseleave = () => {
    if (props.disabled) return
    innerVisible.value = false
    emit('update:visible', false)
    emit('visibleChange', false)
  }

  const handleContentClick = (event: MouseEvent) => {
    event.stopPropagation()
  }

  const handleContentKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      innerVisible.value = false
      emit('update:visible', false)
      emit('visibleChange', false)
    }
  }

  // 点击外部关闭功能
  const handleClickOutside = (event: MouseEvent) => {
    if (!props.closeOnClickOutside || !visible.value) return

    const target = event.target as HTMLElement
    const triggerEl = dropdownInnerRef.value?.triggerRef
    const contentEl = dropdownInnerRef.value?.contentRef

    if (!triggerEl || !contentEl) return

    // 如果点击的是触发器或内容区域，不处理
    if (triggerEl.contains(target) || contentEl.contains(target)) {
      return
    }

    // 关闭下拉框
    innerVisible.value = false
    emit('update:visible', false)
    emit('visibleChange', false)
  }

  // 生命周期管理
  onMounted(() => {
    document.addEventListener('click', handleClickOutside, true)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside, true)
  })

  // 创建下拉菜单上下文
  const dropdownContext = {
    props,
    visible,
    handleUpdateVisible: (newVisible: boolean) => {
      innerVisible.value = newVisible
      emit('update:visible', newVisible)
      emit('visibleChange', newVisible)
    },
    handleSelect: () => {
      if (props.closeOnSelect) {
        innerVisible.value = false
        emit('update:visible', false)
        emit('visibleChange', false)
      }
    },
    handleClickOutside: () => {
      if (props.closeOnClickOutside) {
        innerVisible.value = false
        emit('update:visible', false)
        emit('visibleChange', false)
      }
    },
  }

  // 提供上下文给子组件
  provide(dropdownKey, dropdownContext)
</script>
