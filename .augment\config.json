{"project": {"name": "Speed UI", "type": "vue-component-library", "architecture": "monorepo", "framework": "vue3-typescript"}, "rules": {"codeGeneration": {"componentPrefix": "sp-", "scriptSetup": true, "typescript": true, "bemNaming": true, "singleAttributePerLine": true, "testRequired": true, "documentationRequired": true, "themeSupport": true, "customClassName": true}, "fileStructure": {"componentDir": "packages/ui/src/components/{name}/", "styleDir": "packages/theme-default/src/", "requiredFiles": ["index.ts", "{name}.vue", "{name}.ts", "README.md", "__tests__/{name}.test.ts"], "styleFiles": ["packages/theme-default/src/{name}.scss"]}, "naming": {"components": "PascalCase", "files": "kebab-case", "cssClasses": "BEM", "cssVariables": "--sp-{component}-{property}", "themeVariables": "--sp-color-primary", "themeClassName": "sp-{component}-custom", "types": "PascalCase", "interfaces": "PascalCase"}, "testing": {"framework": "vitest", "coverage": "> 80%", "requiredTests": ["基础功能", "Props 测试", "事件测试", "状态测试"]}, "dependencies": {"packageManager": "pnpm", "manualEdit": false, "useWorkspaceProtocol": true}}, "preferences": {"codeStyle": {"prettier": true, "eslint": true, "singleQuote": true, "semi": false, "tabWidth": 2}, "documentation": {"jsdoc": true, "readme": true, "examples": true, "apiDocs": true}}, "restrictions": {"forbidden": ["直接修改 package.json 依赖", "破坏 API 兼容性", "使用内联样式", "忽略 TypeScript 检查", "跳过测试"], "cautious": ["修改 BEM 辅助工具", "更改构建配置", "修改全局样式", "重构公共工具"]}, "templates": {"component": {"vue": "script setup + typescript", "props": "interface + withDefaults", "emits": "interface + defineEmits", "className": "BEM + ClassBuilder pattern"}, "test": {"framework": "vitest", "structure": "describe blocks", "coverage": "all props and emits"}}}