<template>
  <ListInner
    :class="listClasses"
    :style="listStyles"
    :aria-multiselectable="props.multiple"
    :aria-label="props.ariaLabel"
  >
    <slot />
  </ListInner>
</template>

<script setup lang="ts">
  import { computed, provide } from 'vue'
  import { bemHelper } from '@speed-ui/config'
  import type { ListProps, ListContext } from './types'
  import { listKey } from './constants'
  import ListInner from './ListInner.vue'

  // 逻辑处理层：状态管理、业务逻辑、事件处理、上下文提供
  const props = withDefaults(defineProps<ListProps>(), {
    size: 'medium',
    bordered: false,
    split: true,
    hoverable: true,
    selectable: false,
    multiple: false,
    selectedKeys: () => [],
    loading: false,
  })

  const emit = defineEmits<{
    'update:selectedKeys': [keys: string[]]
    select: [key: string, selected: boolean, selectedKeys: string[]]
    click: [key: string, event: MouseEvent]
  }>()

  // BEM helper
  const bem = bemHelper('list')

  // 计算样式类
  const listClasses = computed(() => [
    bem.b(), // sp-list
    bem.m(props.size), // sp-list--small/medium/large
    bem.m(props.variant || 'default'), // sp-list--default/simple
    {
      [bem.m('bordered')]: props.bordered,
      [bem.m('split')]: props.split,
      [bem.m('hoverable')]: props.hoverable,
      [bem.m('selectable')]: props.selectable,
      [bem.m('loading')]: props.loading,
    },
  ])

  // 计算样式
  const listStyles = computed(() => {
    const styles: Record<string, any> = {}
    if (props.maxHeight) {
      styles.maxHeight =
        typeof props.maxHeight === 'number'
          ? `${props.maxHeight}px`
          : props.maxHeight
    }
    return styles
  })

  // 选择处理逻辑
  const handleItemSelect = (key: string, selected: boolean) => {
    try {
      if (!props.selectable) return
      if (!key) {
        console.warn('List: 选择项缺少 key 属性')
        return
      }

      let newSelectedKeys: string[]

      if (props.multiple) {
        if (selected) {
          newSelectedKeys = [...props.selectedKeys, key]
        } else {
          newSelectedKeys = props.selectedKeys.filter(k => k !== key)
        }
      } else {
        newSelectedKeys = selected ? [key] : []
      }

      emit('update:selectedKeys', newSelectedKeys)
      emit('select', key, selected, newSelectedKeys)
    } catch (error) {
      console.error('List selection error:', error)
    }
  }

  // 点击处理逻辑
  const handleItemClick = (key: string, event: MouseEvent) => {
    try {
      if (!key) {
        console.warn('List: 点击项缺少 key 属性')
        return
      }
      emit('click', key, event)
    } catch (error) {
      console.error('List click error:', error)
    }
  }

  // 列表上下文
  const listContext: ListContext = {
    props,
    selectedKeys: computed(() => props.selectedKeys),
    onItemSelect: handleItemSelect,
    onItemClick: handleItemClick,
  }

  // 提供上下文给子组件
  provide(listKey, listContext)
</script>
