# setTheme 方法使用说明

## 🔍 **setTheme 方法的来源**

`setTheme` 方法来自于 `@speed-ui/hooks` 包，但为了用户便利，在 `@speed-ui/ui` 包中重新导出了。

### **实际定义位置**
```
packages/hooks/src/use-theme/index.ts (第 248-250 行)

export const setTheme = (theme: ThemeConfig | PresetThemeName) => {
  globalThemeManager.setTheme(theme)
}
```

### **重新导出位置**
```
packages/ui/src/index.ts (第 68-76 行)

export {
  useTheme,
  setTheme,        // ← 这里重新导出
  setPrimaryColor,
  resetTheme,
  PRESET_THEMES,
  type ThemeConfig,
  type PresetThemeName,
} from '@speed-ui/hooks'
```

## 🚀 **用户使用方式**

### **方式 1: 从 UI 包导入（推荐）**
```typescript
import SpeedUI, { setTheme } from '@speed-ui/ui'

// 设置预设主题
setTheme('blue')

// 设置自定义主题
setTheme({
  primaryColor: '#1890ff',
  successColor: '#52c41a'
})
```

### **方式 2: 直接从 hooks 包导入**
```typescript
import { setTheme } from '@speed-ui/hooks'

setTheme('blue')
```

### **方式 3: 使用 useTheme hook**
```typescript
import { useTheme } from '@speed-ui/ui'

const { setTheme } = useTheme()
setTheme('blue')
```

## 📋 **setTheme 方法签名**

```typescript
function setTheme(theme: ThemeConfig | PresetThemeName): void

// 预设主题名称
type PresetThemeName = 'default' | 'blue' | 'green' | 'red' | 'orange' | 'purple'

// 主题配置接口
interface ThemeConfig {
  primaryColor?: string
  successColor?: string
  warningColor?: string
  dangerColor?: string
  infoColor?: string
  borderRadius?: string
  fontSize?: string
  fontFamily?: string
}
```

## 💡 **使用示例**

### **设置预设主题**
```typescript
import { setTheme } from '@speed-ui/ui'

// 蓝色主题
setTheme('blue')

// 绿色主题
setTheme('green')

// 红色主题
setTheme('red')
```

### **设置自定义主题**
```typescript
import { setTheme } from '@speed-ui/ui'

// 完整主题配置
setTheme({
  primaryColor: '#ff6b6b',
  successColor: '#51cf66',
  warningColor: '#ffd43b',
  dangerColor: '#ff6b6b',
  borderRadius: '8px',
  fontSize: '16px'
})

// 只设置主色调
setTheme({
  primaryColor: '#1890ff'
})
```

### **在 Vue 组件中使用**
```vue
<template>
  <div>
    <SpButton primary>主要按钮</SpButton>
    <button @click="changeTheme">切换主题</button>
  </div>
</template>

<script setup>
import { SpButton, setTheme } from '@speed-ui/ui'

const changeTheme = () => {
  setTheme('blue')
}
</script>
```

### **在 main.ts 中初始化主题**
```typescript
// main.ts
import { createApp } from 'vue'
import SpeedUI, { setTheme } from '@speed-ui/ui'
import '@speed-ui/theme-default/lib/index.css'
import App from './App.vue'

const app = createApp(App)
app.use(SpeedUI)

// 设置初始主题
setTheme({
  primaryColor: '#1890ff',
  borderRadius: '6px'
})

app.mount('#app')
```

## 🔧 **依赖关系**

```
@speed-ui/ui
├── @speed-ui/hooks (包含 setTheme)
├── @speed-ui/bem-helper
├── @speed-ui/utils
└── vue (peer)
```

当用户安装 `@speed-ui/ui` 时，会自动安装 `@speed-ui/hooks` 依赖，所以可以直接使用 `setTheme` 方法。

## ⚠️ **注意事项**

1. **确保样式文件已导入**: 需要导入 `@speed-ui/theme-default/lib/index.css`
2. **主题持久化**: `setTheme` 会自动保存到 localStorage
3. **CSS 变量**: 主题变化会自动更新 CSS 变量
4. **类型安全**: 使用 TypeScript 时会有完整的类型提示

## 🎯 **总结**

`setTheme` 方法虽然定义在 `@speed-ui/hooks` 包中，但为了用户便利，在 `@speed-ui/ui` 包中重新导出了。用户可以直接从 UI 包导入使用，无需单独安装 hooks 包。
