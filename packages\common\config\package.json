{"name": "@speed-ui/config", "version": "1.0.0", "description": "Speed UI 配置工具包", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist"}, "peerDependencies": {"vue": "^3.0.0"}, "dependencies": {}, "devDependencies": {}}