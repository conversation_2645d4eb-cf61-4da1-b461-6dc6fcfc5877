<template>
  <div class="custom-layout-wrapper">
    <!-- 自定义导航栏 -->
    <CustomNavbar />
    
    <!-- 自定义侧边栏 - 只在有内容时显示 -->
    <CustomSidebar v-if="shouldShowSidebar" />
    
    <!-- VitePress 默认布局，但隐藏其导航栏和侧边栏 -->
    <Layout :class="{ 'no-sidebar': !shouldShowSidebar, 'has-toc': shouldShowToc }" />
    
    <!-- 右侧目录 -->
    <CustomToc v-if="shouldShowToc" />
  </div>
</template>

<script setup lang="ts">
import DefaultTheme from 'vitepress/theme'
import { useRoute, useData } from 'vitepress'
import { computed } from 'vue'
import CustomNavbar from './CustomNavbar.vue'
import CustomSidebar from './CustomSidebar.vue'
import CustomToc from './CustomToc.vue'

const { Layout } = DefaultTheme
const route = useRoute()
const { theme } = useData()

// 判断是否应该显示侧边栏
const shouldShowSidebar = computed(() => {
  const sidebar = theme.value.sidebar
  if (!sidebar) return false
  
  const currentPath = route.path
  
  // 检查当前路径是否匹配任何侧边栏配置
  for (const path of Object.keys(sidebar)) {
    if (currentPath.startsWith(path)) {
      return true
    }
  }
  
  return false
})

// 判断是否应该显示右侧目录
const shouldShowToc = computed(() => {
  // 在文档页面显示目录
  return route.path.startsWith('/components/') || route.path.startsWith('/guide/')
})
</script>

<style>
.custom-layout-wrapper {
  position: relative;
}

/* 全局暗黑模式支持 */
:global(.dark) {
  background-color: #1a1a1a;
  color: #e5e7eb;
}

:global(.dark) body {
  background-color: #1a1a1a;
  color: #e5e7eb;
}

:global(.dark) .VPContent {
  background-color: #1a1a1a !important;
}

:global(.dark) .VPDoc {
  background-color: #1a1a1a !important;
}

:global(.dark) .vp-doc {
  background-color: #1a1a1a !important;
  color: #e5e7eb !important;
}

:global(.dark) .vp-doc h1,
:global(.dark) .vp-doc h2,
:global(.dark) .vp-doc h3,
:global(.dark) .vp-doc h4,
:global(.dark) .vp-doc h5,
:global(.dark) .vp-doc h6 {
  color: #f3f4f6 !important;
}

:global(.dark) .vp-doc p {
  color: #d1d5db !important;
}

:global(.dark) .vp-doc code {
  background-color: #374151 !important;
  color: #f9fafb !important;
}

:global(.dark) .vp-doc pre {
  background-color: #1f2937 !important;
}

:global(.dark) .vp-doc blockquote {
  border-left-color: #6b7280 !important;
  background-color: #1f2937 !important;
  color: #d1d5db !important;
}

/* 隐藏默认的 VitePress 导航栏 */
:deep(.VPNav) {
  display: none !important;
}

/* 隐藏默认的 VitePress 侧边栏 */
:deep(.VPSidebar) {
  display: none !important;
}

/* 隐藏默认的 VitePress 右侧目录 */
:deep(.VPDocAside) {
  display: none !important;
}

:deep(.VPDocAsideOutline) {
  display: none !important;
}

/* 调整 VitePress 布局的顶部和左侧间距 */
:deep(.Layout) {
  padding-top: 60px; /* 为自定义导航栏留出空间 */
  padding-left: 280px; /* 为自定义侧边栏留出空间 */
  padding-right: 0;
}

/* 没有侧边栏时的布局 */
:deep(.Layout.no-sidebar) {
  padding-left: 0; /* 没有侧边栏时不需要左侧间距 */
}

/* 没有侧边栏时的内容区域 */
:deep(.Layout.no-sidebar .VPContent) {
  margin-left: 0 !important;
  width: 100% !important;
}

/* 有右侧目录时的布局 */
:deep(.Layout.has-toc) {
  padding-right: 280px; /* 为右侧目录留出空间 */
}

/* 调整内容区域 */
:deep(.VPContent) {
  padding-top: 0 !important;
  margin-left: 250px !important; /* 为侧边栏留出空间 */
  min-width: none !important;
  max-width: none !important;
  width: calc(100% - 250px) !important; /* 减去侧边栏宽度 */
}


/* 调整文档内容区域 */
:deep(.VPDoc) {
  padding-left: 24px !important;
  padding-right: 24px !important;
}

/* 响应式设计已移除 - 取消媒体查询限制 */
</style>