import type { CSSProperties } from 'vue'

/**
 * 样式工具类型定义
 */
export type StyleValue = string | CSSProperties | Array<StyleValue> | undefined

/**
 * 尺寸映射接口
 */
export interface SizeMap {
  small: string | number
  medium: string | number
  large: string | number
  huge?: string | number
}

/**
 * 主题颜色类型
 */
export type ThemeColor = 'primary' | 'success' | 'warning' | 'danger' | 'info'

/**
 * 组件状态类型
 */
export type ComponentStatus = 'default' | 'success' | 'warning' | 'error' | 'validating'

/**
 * 合并样式对象
 * 将多个样式对象合并为一个
 * @param styles 样式对象数组
 * @returns 合并后的样式对象
 */
export function mergeStyles(...styles: (CSSProperties | undefined)[]): CSSProperties {
  const result: CSSProperties = {}
  
  styles.forEach(style => {
    if (style && typeof style === 'object') {
      Object.assign(result, style)
    }
  })
  
  return result
}

/**
 * 标准化样式值
 * 将各种类型的样式值转换为标准的CSSProperties对象
 * @param style 样式值
 * @returns 标准化的样式对象
 */
export function normalizeStyle(style: StyleValue): CSSProperties {
  if (!style) return {}
  
  if (typeof style === 'string') {
    // 解析字符串样式（如："color: red; font-size: 14px;"）
    const result: CSSProperties = {}
    style.split(';').forEach(declaration => {
      const [property, value] = declaration.split(':').map(s => s.trim())
      if (property && value) {
        // 转换kebab-case为camelCase
        const camelProperty = property.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase())
        result[camelProperty as keyof CSSProperties] = value as any
      }
    })
    return result
  }
  
  if (Array.isArray(style)) {
    // 合并数组中的所有样式
    return style.reduce((acc, curr) => {
      return mergeStyles(acc, normalizeStyle(curr))
    }, {})
  }
  
  return style as CSSProperties
}

/**
 * 根据尺寸获取对应的值
 * @param size 尺寸类型
 * @param sizeMap 尺寸映射表
 * @returns 对应的尺寸值
 */
export function getSizeValue(
  size: 'small' | 'medium' | 'large' | 'huge',
  sizeMap: SizeMap
): string | number {
  return sizeMap[size] ?? sizeMap.medium
}

/**
 * 生成响应式尺寸样式
 * @param size 尺寸类型
 * @param property CSS属性名
 * @param sizeMap 尺寸映射表
 * @returns 样式对象
 */
export function createSizeStyle(
  size: 'small' | 'medium' | 'large' | 'huge',
  property: keyof CSSProperties,
  sizeMap: SizeMap
): CSSProperties {
  const value = getSizeValue(size, sizeMap)
  return {
    [property]: typeof value === 'number' ? `${value}px` : value
  }
}

/**
 * 根据状态获取对应的颜色
 * @param status 组件状态
 * @returns 对应的CSS颜色值
 */
export function getStatusColor(status: ComponentStatus): string {
  const colorMap: Record<ComponentStatus, string> = {
    default: 'var(--sp-color-text-primary, #333333)',
    success: 'var(--sp-color-success, #52c41a)',
    warning: 'var(--sp-color-warning, #faad14)',
    error: 'var(--sp-color-error, #ff4d4f)',
    validating: 'var(--sp-color-primary, #1890ff)'
  }
  
  return colorMap[status] || colorMap.default
}

/**
 * 根据主题颜色获取对应的CSS变量
 * @param color 主题颜色
 * @param variant 颜色变体（hover, active等）
 * @returns CSS变量字符串
 */
export function getThemeColor(color: ThemeColor, variant?: 'hover' | 'active' | 'light' | 'lightest'): string {
  const baseVar = `--sp-color-${color}`
  
  if (!variant) {
    return `var(${baseVar})`
  }
  
  return `var(${baseVar}-${variant})`
}

/**
 * 创建阴影样式
 * @param level 阴影级别（1-5）
 * @returns 阴影样式对象
 */
export function createShadowStyle(level: 1 | 2 | 3 | 4 | 5): CSSProperties {
  const shadows = {
    1: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
    2: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
    3: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
    4: '0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)',
    5: '0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)'
  }
  
  return {
    boxShadow: shadows[level]
  }
}

/**
 * 创建过渡动画样式
 * @param properties 需要过渡的属性
 * @param duration 动画时长
 * @param timing 时间函数
 * @returns 过渡样式对象
 */
export function createTransitionStyle(
  properties: string[] = ['all'],
  duration: string = '0.3s',
  timing: string = 'ease'
): CSSProperties {
  const transition = properties
    .map(prop => `${prop} ${duration} ${timing}`)
    .join(', ')
  
  return {
    transition
  }
}

/**
 * 创建边框样式
 * @param width 边框宽度
 * @param style 边框样式
 * @param color 边框颜色
 * @returns 边框样式对象
 */
export function createBorderStyle(
  width: string = '1px',
  style: string = 'solid',
  color: string = 'var(--sp-border-color-base, #d9d9d9)'
): CSSProperties {
  return {
    border: `${width} ${style} ${color}`
  }
}

/**
 * 创建圆角样式
 * @param radius 圆角大小
 * @returns 圆角样式对象
 */
export function createBorderRadiusStyle(radius: string | number): CSSProperties {
  const value = typeof radius === 'number' ? `${radius}px` : radius
  return {
    borderRadius: value
  }
}

/**
 * 创建flex布局样式
 * @param direction flex方向
 * @param justify 主轴对齐方式
 * @param align 交叉轴对齐方式
 * @param wrap 是否换行
 * @returns flex样式对象
 */
export function createFlexStyle(
  direction: 'row' | 'column' | 'row-reverse' | 'column-reverse' = 'row',
  justify: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly' = 'flex-start',
  align: 'flex-start' | 'flex-end' | 'center' | 'baseline' | 'stretch' = 'flex-start',
  wrap: 'nowrap' | 'wrap' | 'wrap-reverse' = 'nowrap'
): CSSProperties {
  return {
    display: 'flex',
    flexDirection: direction,
    justifyContent: justify,
    alignItems: align,
    flexWrap: wrap
  }
}

/**
 * 创建文本省略样式
 * @param lines 显示行数，1为单行省略，大于1为多行省略
 * @returns 文本省略样式对象
 */
export function createTextEllipsisStyle(lines: number = 1): CSSProperties {
  if (lines === 1) {
    return {
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      whiteSpace: 'nowrap'
    }
  }
  
  return {
    display: '-webkit-box',
    WebkitBoxOrient: 'vertical',
    WebkitLineClamp: lines,
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  }
}

/**
 * 创建绝对定位样式
 * @param position 位置配置
 * @returns 定位样式对象
 */
export function createAbsoluteStyle(position: {
  top?: string | number
  right?: string | number
  bottom?: string | number
  left?: string | number
}): CSSProperties {
  const style: CSSProperties = {
    position: 'absolute'
  }
  
  Object.entries(position).forEach(([key, value]) => {
    if (value !== undefined) {
      style[key as keyof CSSProperties] = typeof value === 'number' ? `${value}px` : value
    }
  })
  
  return style
}

/**
 * 样式工具类
 * 提供链式调用的样式构建功能
 */
export class StyleBuilder {
  private styles: CSSProperties = {}
  
  /**
   * 添加样式
   */
  add(style: CSSProperties): this {
    Object.assign(this.styles, style)
    return this
  }
  
  /**
   * 设置尺寸
   */
  size(width?: string | number, height?: string | number): this {
    if (width !== undefined) {
      this.styles.width = typeof width === 'number' ? `${width}px` : width
    }
    if (height !== undefined) {
      this.styles.height = typeof height === 'number' ? `${height}px` : height
    }
    return this
  }
  
  /**
   * 设置边距
   */
  margin(top?: string | number, right?: string | number, bottom?: string | number, left?: string | number): this {
    if (top !== undefined) {
      this.styles.marginTop = typeof top === 'number' ? `${top}px` : top
    }
    if (right !== undefined) {
      this.styles.marginRight = typeof right === 'number' ? `${right}px` : right
    }
    if (bottom !== undefined) {
      this.styles.marginBottom = typeof bottom === 'number' ? `${bottom}px` : bottom
    }
    if (left !== undefined) {
      this.styles.marginLeft = typeof left === 'number' ? `${left}px` : left
    }
    return this
  }
  
  /**
   * 设置内边距
   */
  padding(top?: string | number, right?: string | number, bottom?: string | number, left?: string | number): this {
    if (top !== undefined) {
      this.styles.paddingTop = typeof top === 'number' ? `${top}px` : top
    }
    if (right !== undefined) {
      this.styles.paddingRight = typeof right === 'number' ? `${right}px` : right
    }
    if (bottom !== undefined) {
      this.styles.paddingBottom = typeof bottom === 'number' ? `${bottom}px` : bottom
    }
    if (left !== undefined) {
      this.styles.paddingLeft = typeof left === 'number' ? `${left}px` : left
    }
    return this
  }
  
  /**
   * 设置颜色
   */
  color(color: string): this {
    this.styles.color = color
    return this
  }
  
  /**
   * 设置背景色
   */
  backgroundColor(color: string): this {
    this.styles.backgroundColor = color
    return this
  }
  
  /**
   * 设置字体大小
   */
  fontSize(size: string | number): this {
    this.styles.fontSize = typeof size === 'number' ? `${size}px` : size
    return this
  }
  
  /**
   * 构建最终样式
   */
  build(): CSSProperties {
    return { ...this.styles }
  }
  
  /**
   * 重置样式
   */
  reset(): this {
    this.styles = {}
    return this
  }
}

/**
 * 创建样式构建器实例
 */
export function createStyleBuilder(): StyleBuilder {
  return new StyleBuilder()
}