# Button 组件类名生成优化

## 优化概述

我们对 Button 组件的类名生成进行了全面优化，提供了更好的类型安全、可维护性和性能。

## 主要改进

### 1. 类型安全增强

**优化前：**
```typescript
// 硬编码的字符串，容易出错
if (props.primary) classes.push(bem.m('primary'))
else if (props.secondary) classes.push(bem.m('secondary'))
// ... 长长的 if-else 链
```

**优化后：**
```typescript
// 类型安全的枚举和接口
export type ButtonVariant = 'primary' | 'secondary' | 'dashed' | 'text' | 'link'
export type ButtonColorType = 'default' | 'success' | 'warning' | 'danger'
export type ButtonSize = 'small' | 'medium' | 'large' | 'huge'
export type ButtonShape = 'circle' | 'round' | 'square'
```

### 2. 逻辑优化

**优化前的问题：**
- 硬编码的固定类名 `bem.s('custom')` 和 `bem.s('active')` 总是被添加
- 冗长的 if-else 链处理按钮类型
- 形状属性没有互斥处理
- 缺少对无效组合的处理

**优化后的解决方案：**
```typescript
// 按钮变体优先级处理
private getVariant(): ButtonVariant {
  if (this.props.primary) return 'primary'
  if (this.props.secondary) return 'secondary'
  if (this.props.dashed) return 'dashed'
  if (this.props.text) return 'text'
  if (this.props.link) return 'link'
  return 'secondary' // 默认类型
}

// 形状互斥处理
private getShape(): ButtonShape | null {
  if (this.props.circle) return 'circle'
  if (this.props.round) return 'round'
  if (this.props.square) return 'square'
  return null
}
```

### 3. 架构改进

**新增 ButtonClassBuilder 类：**
- 职责单一：专门负责类名生成
- 易于测试：可以独立测试类名生成逻辑
- 易于扩展：新增类名规则只需修改构建器
- 调试友好：提供调试信息方法

### 4. 性能优化

**优化前：**
```typescript
// 每次都执行所有判断
const buttonClassName = computed(() => {
  const classes = [bem.b(), bem.s('custom'), bem.s('active')]
  // 大量的 if 判断...
  return classes
})
```

**优化后：**
```typescript
// 分类处理，逻辑更清晰
const buttonClassName = computed(() => {
  const builder = createButtonClassBuilder(bem, props, buttonState.value)
  return builder.build()
})
```

## 使用示例

### 基础用法
```vue
<template>
  <!-- 自动生成：['sp-button', 'sp-button--primary', 'sp-button--medium'] -->
  <Button primary>主要按钮</Button>
  
  <!-- 自动生成：['sp-button', 'sp-button--secondary', 'sp-button--success', 'sp-button--large', 'sp-button--circle'] -->
  <Button secondary type="success" size="large" circle>成功按钮</Button>
</template>
```

### 优先级处理
```vue
<template>
  <!-- primary 优先级最高，只会生成 sp-button--primary -->
  <Button primary secondary text>按钮</Button>
  
  <!-- circle 优先级最高，只会生成 sp-button--circle -->
  <Button circle round square>按钮</Button>
</template>
```

### 状态处理
```vue
<template>
  <!-- 自动处理状态类名 -->
  <Button 
    primary 
    :disabled="isDisabled"
    :loading="isLoading"
    @click="handleClick"
  >
    状态按钮
  </Button>
</template>
```

## 生成的类名结构

```
sp-button                    // 基础类名
sp-button--{variant}         // 变体：primary | secondary | dashed | text | link
sp-button--{colorType}       // 颜色：default | success | warning | danger
sp-button--{size}           // 尺寸：small | medium | large | huge
sp-button--{shape}          // 形状：circle | round | square
sp-button--{state}          // 状态：disabled | loading | active | pressing | countdown
sp-button--{layout}         // 布局：vertical
```

## 调试支持

```typescript
// 开发环境下可以获取调试信息
const builder = createButtonClassBuilder(bem, props, state)
console.log(builder.getDebugInfo())
// 输出：
// {
//   variant: 'primary',
//   colorType: 'success',
//   size: 'large',
//   shape: 'circle',
//   state: { isActive: false, isPressing: false, isCountingDown: false },
//   props: { disabled: false, loading: false, vertical: false }
// }
```

## 测试覆盖

我们为类名构建器提供了完整的单元测试，覆盖：
- 基础功能测试
- 变体类型优先级测试
- 形状类型互斥测试
- 颜色类型处理测试
- 状态类名测试
- 布局类名测试
- 调试信息测试

运行测试：
```bash
npm test packages/ui/src/components/button/__tests__/button-class-builder.test.ts
```

## 向后兼容性

此次优化完全向后兼容，现有的 Button 组件使用方式不需要任何改动。

## 总结

通过这次优化，我们实现了：

1. **更好的类型安全**：使用 TypeScript 类型定义防止错误
2. **更清晰的逻辑**：分离关注点，每个方法职责单一
3. **更好的性能**：减少不必要的类名生成
4. **更易维护**：模块化设计，易于扩展和修改
5. **更好的测试**：独立的类名构建器易于测试
6. **更好的调试**：提供调试信息帮助开发

这些改进使得 Button 组件的类名生成更加健壮、可靠和易于维护。
