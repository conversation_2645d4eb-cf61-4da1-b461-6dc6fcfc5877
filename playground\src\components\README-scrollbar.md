# Scrollbar 滚动条组件

一个美观的自定义滚动条组件，可以包裹任何元素，让滚动条变成好看的样式！

## 特性

- 🎨 **美观的样式** - 自定义滚动条样式，比原生滚动条更美观
- 📱 **响应式设计** - 支持垂直和水平滚动
- 🎯 **多种尺寸** - 提供 small、medium、large 三种尺寸
- 🌈 **主题支持** - 支持浅色、深色主题
- 🔧 **程序控制** - 提供 API 方法控制滚动位置
- ⚡ **高性能** - 使用原生滚动，性能优异
- 🎛️ **灵活配置** - 支持始终显示、原生模式等配置

## 基础用法

```vue
<template>
  <Scrollbar max-height="200px">
    <div>
      <!-- 你的内容 -->
      <p v-for="i in 20" :key="i">第 {{ i }} 行内容</p>
    </div>
  </Scrollbar>
</template>

<script setup>
import Scrollbar from './scrollbar.vue'
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `maxHeight` | `string \| number` | `undefined` | 最大高度 |
| `maxWidth` | `string \| number` | `undefined` | 最大宽度 |
| `always` | `boolean` | `false` | 是否始终显示滚动条 |
| `size` | `'small' \| 'medium' \| 'large'` | `'medium'` | 滚动条尺寸 |
| `native` | `boolean` | `false` | 是否使用原生滚动条 |
| `theme` | `'light' \| 'dark' \| 'auto'` | `'auto'` | 滚动条主题 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `scroll` | `(event: Event)` | 滚动时触发 |

## 方法

通过 `ref` 可以调用以下方法：

| 方法名 | 参数 | 说明 |
|--------|------|------|
| `scrollTo` | `(options: ScrollToOptions \| number, y?: number)` | 滚动到指定位置 |
| `scrollTop` | `(top: number)` | 设置垂直滚动位置 |
| `scrollLeft` | `(left: number)` | 设置水平滚动位置 |
| `update` | `()` | 更新滚动条状态 |

## 示例

### 基础滚动

```vue
<Scrollbar max-height="200px">
  <div class="content">
    <!-- 长内容 -->
  </div>
</Scrollbar>
```

### 水平滚动

```vue
<Scrollbar max-width="300px" max-height="100px">
  <div style="width: 800px;">
    <!-- 宽内容 -->
  </div>
</Scrollbar>
```

### 不同尺寸

```vue
<!-- 小尺寸 -->
<Scrollbar size="small" max-height="150px">
  <div>内容</div>
</Scrollbar>

<!-- 大尺寸 -->
<Scrollbar size="large" max-height="150px">
  <div>内容</div>
</Scrollbar>
```

### 主题

```vue
<!-- 浅色主题 -->
<Scrollbar theme="light" max-height="150px">
  <div>内容</div>
</Scrollbar>

<!-- 深色主题 -->
<Scrollbar theme="dark" max-height="150px">
  <div>内容</div>
</Scrollbar>
```

### 始终显示

```vue
<Scrollbar :always="true" max-height="150px">
  <div>内容</div>
</Scrollbar>
```

### 程序控制

```vue
<template>
  <div>
    <button @click="scrollToTop">滚动到顶部</button>
    <button @click="scrollToBottom">滚动到底部</button>
    
    <Scrollbar ref="scrollbarRef" max-height="200px">
      <div>长内容</div>
    </Scrollbar>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const scrollbarRef = ref()

const scrollToTop = () => {
  scrollbarRef.value?.scrollTop(0)
}

const scrollToBottom = () => {
  scrollbarRef.value?.scrollTop(999999)
}
</script>
```

### 原生模式

```vue
<Scrollbar :native="true" max-height="150px">
  <div>内容</div>
</Scrollbar>
```

## 样式定制

组件支持主题系统，可以通过 CSS 变量进行定制：

```css
.sp-scrollbar-custom {
  --scrollbar-thumb-color: rgba(0, 0, 0, 0.3);
  --scrollbar-thumb-hover-color: rgba(0, 0, 0, 0.5);
  --scrollbar-track-color: transparent;
}
```

## 注意事项

1. 组件会自动检测内容是否需要滚动条
2. 滚动条只在需要时显示（除非设置 `always` 为 `true`）
3. 支持鼠标拖拽滚动条
4. 支持点击滚动条轨道快速滚动
5. 响应窗口大小变化和内容变化

## 访问演示

在开发环境中，你可以访问以下链接查看完整演示：

```
http://localhost:5173/zh-CN/scrollbar-demo
```
