<!--
  InputField.vue - 接口层
  职责：对外暴露API，接收props和events，处理业务逻辑
-->

<template>
  <InputFieldCore
    ref="inputFieldCoreRef"
    v-bind="coreProps"
    @update:value="handleValueUpdate"
    @input="handleInput"
    @change="handleChange"
    @focus="handleFocus"
    @blur="handleBlur"
    @clear="handleClear"
    @validate="handleValidate"
  >
    <!-- 透传所有插槽 -->
    <template #prefix>
      <slot name="prefix" />
    </template>
    <template #suffix>
      <slot name="suffix" />
    </template>
  </InputFieldCore>
</template>

<script setup lang="ts">
  import { ref, computed, inject } from 'vue'
  import { useVModel } from '@speed-ui/hooks'
  import InputFieldCore from './input-field-core.vue'
  import type { InputFieldProps, InputFieldEmits } from './types'
  import { inputFieldPropsDefaults } from './types'
  import type { FormContext } from '../Form/types'

  /**
   * InputField 组件 - 接口层
   *
   * 结合 Input 和 FormItem 功能的综合组件：
   * input-field.vue (接口层) → input-field-core.vue (实现层)
   */

  const props = withDefaults(
    defineProps<InputFieldProps>(),
    inputFieldPropsDefaults
  )
  const emit = defineEmits<InputFieldEmits>()

  // 表单上下文注入
  const formContext = inject<FormContext>('spForm', {})

  // 组件引用
  const inputFieldCoreRef = ref<InstanceType<typeof InputFieldCore> | null>(
    null
  )

  // 状态管理
  const focused = ref(false)

  // 使用 useVModel 进行双向绑定
  const {
    value: currentValue,
    updateValue,
    hasValue: hasValueComputed,
  } = useVModel<string | number | undefined>(props, emit, {
    prop: 'value',
    event: 'update:value',
    defaultValue: props.value,
    onAfterUpdate: newValue => {
      emit('change', newValue)
    },
    debug: false,
  })

  // 从表单上下文继承配置
  const computedSize = computed(() => {
    return props.size || formContext?.size || 'medium'
  })

  const computedDisabled = computed(() => {
    return props.disabled || formContext?.disabled || false
  })

  const computedShowMessage = computed(() => {
    return props.showMessage ?? formContext?.showMessage ?? true
  })

  // 传递给核心层的属性
  const coreProps = computed(() => ({
    ...props,
    value: currentValue.value,
    size: computedSize.value,
    disabled: computedDisabled.value,
    showMessage: computedShowMessage.value,
    focused: focused.value,
    hasValue: hasValueComputed.value,
  }))

  // 事件处理
  const handleValueUpdate = (value: string | number | undefined) => {
    updateValue(value)
  }

  const handleInput = (event: Event) => {
    emit('input', event)
  }

  const handleChange = (value: string | number | undefined) => {
    // change 事件已经通过 useVModel 的 onAfterUpdate 处理
  }

  const handleFocus = (event: FocusEvent) => {
    focused.value = true
    emit('focus', event)
  }

  const handleBlur = (event: FocusEvent) => {
    focused.value = false
    emit('blur', event)
  }

  const handleClear = () => {
    updateValue(undefined)
    emit('clear')
  }

  const handleValidate = (name: string, isValid: boolean, message: string) => {
    emit('validate', name, isValid, message)
  }

  // 暴露的方法
  const focus = () => {
    inputFieldCoreRef.value?.focus?.()
  }

  const blur = () => {
    inputFieldCoreRef.value?.blur?.()
  }

  const select = () => {
    inputFieldCoreRef.value?.select?.()
  }

  const clear = () => {
    updateValue(undefined)
    emit('clear')
  }

  const validate = async (): Promise<boolean> => {
    return (await inputFieldCoreRef.value?.validate?.()) || false
  }

  const resetField = () => {
    inputFieldCoreRef.value?.resetField?.()
  }

  const clearValidate = () => {
    inputFieldCoreRef.value?.clearValidate?.()
  }

  // 暴露给外部使用
  defineExpose({
    /** 使输入框获得焦点 */
    focus,
    /** 使输入框失去焦点 */
    blur,
    /** 选中输入框中的文字 */
    select,
    /** 清空输入框 */
    clear,
    /** 验证字段 */
    validate,
    /** 重置字段 */
    resetField,
    /** 清除验证 */
    clearValidate,
    /** 输入框元素引用 */
    get input() {
      return inputFieldCoreRef.value?.input || null
    },
    /** 包装器元素引用 */
    get wrapper() {
      return inputFieldCoreRef.value?.wrapper || null
    },
  })
</script>

<script lang="ts">
  export default {
    name: 'SpInputField',
  }
</script>
