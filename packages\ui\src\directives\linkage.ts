import type { Directive, DirectiveBinding } from 'vue'
import { nextTick, watch } from 'vue'

// 指令配置接口
export interface LinkageConfig {
  target: string | string[] // 目标组件的ref名称
  when: string // 监听的属性名
  then: string | LinkageAction // 要设置的目标属性名或动作对象
  condition?: (value: any) => boolean // 自定义条件函数
  transform?: (value: any) => any // 值转换函数
  delay?: number // 延迟执行时间(ms)
}

// 动作配置接口
export interface LinkageAction {
  prop: string // 目标属性名
  value?: any // 固定值
  transform?: (value: any) => any // 值转换函数
}

// 存储指令实例的映射
const linkageInstances = new WeakMap<Element, LinkageInstance>()

// 指令实例类
class LinkageInstance {
  private el: Element
  private binding: DirectiveBinding
  private config: LinkageConfig | LinkageConfig[]
  private unwatchFns: (() => void)[] = []
  private componentInstance: any

  constructor(el: Element, binding: DirectiveBinding) {
    this.el = el
    this.binding = binding
    this.config = binding.value
    // 在Vue 3中，通过binding.instance获取组件实例
    this.componentInstance = binding.instance
    this.init()
  }

  private init() {
    if (!this.componentInstance) {
      console.warn('[v-linkage] 无法获取组件实例')
      return
    }

    const configs = Array.isArray(this.config) ? this.config : [this.config]
    
    configs.forEach(config => {
      this.setupLinkage(config)
    })
  }

  private setupLinkage(config: LinkageConfig) {
    const { when, target, then, condition, transform, delay = 0 } = config
    
    // 创建监听器
    const unwatchFn = watch(
      () => this.getSourceValue(when),
      (newValue) => {
        // 检查条件
        if (condition && !condition(newValue)) {
          return
        }

        // 转换值
        const transformedValue = transform ? transform(newValue) : newValue

        // 延迟执行
        const executeUpdate = () => {
          this.updateTargets(target, then, transformedValue)
        }

        if (delay > 0) {
          setTimeout(executeUpdate, delay)
        } else {
          nextTick(executeUpdate)
        }
      },
      { immediate: true }
    )

    this.unwatchFns.push(unwatchFn)
  }

  private getSourceValue(propName: string): any {
    if (!this.componentInstance) {
      console.warn('[v-linkage] 组件实例不存在')
      return undefined
    }

    // 支持嵌套属性访问，如 'user.name'
    const props = propName.split('.')
    let value = this.componentInstance
    
    for (const prop of props) {
      if (value && typeof value === 'object') {
        // 在Vue 3中，响应式数据可能在不同的位置
        value = value[prop] || value.setupState?.[prop] || value.data?.[prop]
      } else {
        return undefined
      }
    }
    
    // 如果是ref对象，获取其value
    return value?.value !== undefined ? value.value : value
  }

  private updateTargets(targets: string | string[], action: string | LinkageAction, value: any) {
    const targetList = Array.isArray(targets) ? targets : [targets]
    
    targetList.forEach(targetRef => {
      const targetEl = this.findTargetElement(targetRef)
      if (targetEl) {
        this.updateTarget(targetEl, action, value)
      }
    })
  }

  private findTargetElement(refName: string): Element | null {
    // 在当前组件实例中查找ref
    const refs = this.componentInstance?.$refs
    if (refs && refs[refName]) {
      const ref = refs[refName]
      // 如果是组件实例，获取其根元素
      return ref?.$el || ref
    }

    // 如果没找到，尝试在DOM中查找
    const rootEl = this.componentInstance?.$el || this.el.parentElement || document
    return rootEl.querySelector(`[ref="${refName}"]`)
  }

  private updateTarget(targetEl: Element, action: string | LinkageAction, value: any) {
    // 尝试获取Vue组件实例
    const targetInstance = (targetEl as any).__vueParentComponent?.proxy || 
                           (targetEl as any).__vue__ ||
                           this.getComponentInstanceFromElement(targetEl)
    
    if (!targetInstance) {
      // 如果不是Vue组件，直接操作DOM属性
      this.updateDOMElement(targetEl, action, value)
      return
    }

    // 处理Vue组件
    if (typeof action === 'string') {
      // 简单属性设置
      this.setComponentProp(targetInstance, action, value)
    } else {
      // 复杂动作对象
      const finalValue = action.value !== undefined ? action.value : 
                        (action.transform ? action.transform(value) : value)
      this.setComponentProp(targetInstance, action.prop, finalValue)
    }
  }

  private getComponentInstanceFromElement(el: Element): any {
    // 尝试多种方式获取组件实例
    const vueInstance = (el as any).__vueParentComponent?.proxy ||
                       (el as any).__vue__ ||
                       (el as any)._vnode?.component?.proxy
    return vueInstance
  }

  private setComponentProp(instance: any, propName: string, value: any) {
    if (!instance) {
      console.warn('[v-linkage] 目标组件实例不存在')
      return
    }

    // 支持嵌套属性设置
    const props = propName.split('.')
    const lastProp = props.pop()!
    let target = instance
    
    // 导航到目标对象
    for (const prop of props) {
      // 在Vue 3中，尝试多个可能的位置
      const nextTarget = target[prop] || target.setupState?.[prop] || target.data?.[prop]
      if (!nextTarget) {
        // 如果属性不存在，尝试创建
        if (target.setupState && target.setupState[prop] !== undefined) {
          target = target.setupState
        } else if (target.data && target.data[prop] !== undefined) {
          target = target.data
        } else {
          target[prop] = {}
          target = target[prop]
        }
      } else {
        target = nextTarget
      }
    }
    
    // 设置最终属性
    if (target && typeof target === 'object' && target.value !== undefined) {
      // 如果是ref对象，设置其value
      target.value = value
    } else if (instance.setupState && instance.setupState[lastProp] !== undefined) {
      // 尝试设置setupState中的属性
      const setupProp = instance.setupState[lastProp]
      if (setupProp && typeof setupProp === 'object' && setupProp.value !== undefined) {
        setupProp.value = value
      } else {
        instance.setupState[lastProp] = value
      }
    } else if (instance.data && instance.data[lastProp] !== undefined) {
      // 尝试设置data中的属性
      instance.data[lastProp] = value
    } else {
      // 直接设置属性
      target[lastProp] = value
    }
  }

  private updateDOMElement(el: Element, action: string | LinkageAction, value: any) {
    const propName = typeof action === 'string' ? action : action.prop
    const finalValue = typeof action === 'string' ? value : 
                      (action.value !== undefined ? action.value : 
                       (action.transform ? action.transform(value) : value))
    
    // 处理常见的DOM属性
    switch (propName) {
      case 'disabled':
        (el as HTMLInputElement).disabled = Boolean(finalValue)
        break
      case 'readonly':
        (el as HTMLInputElement).readOnly = Boolean(finalValue)
        break
      case 'value':
        (el as HTMLInputElement).value = String(finalValue || '')
        break
      case 'checked':
        (el as HTMLInputElement).checked = Boolean(finalValue)
        break
      case 'hidden':
        (el as HTMLElement).hidden = Boolean(finalValue)
        break
      case 'class':
        if (finalValue) {
          el.classList.add(String(finalValue))
        }
        break
      default:
        // 通用属性设置
        el.setAttribute(propName, String(finalValue))
    }
  }

  public update(newBinding: DirectiveBinding) {
    // 清理旧的监听器
    this.destroy()
    
    // 重新初始化
    this.binding = newBinding
    this.config = newBinding.value
    this.init()
  }

  public destroy() {
    // 清理所有监听器
    this.unwatchFns.forEach(fn => fn())
    this.unwatchFns = []
  }
}

// v-linkage 指令定义
export const vLinkage: Directive = {
  mounted(el: Element, binding: DirectiveBinding) {
    const instance = new LinkageInstance(el, binding)
    linkageInstances.set(el, instance)
  },

  updated(el: Element, binding: DirectiveBinding) {
    const instance = linkageInstances.get(el)
    if (instance) {
      instance.update(binding)
    }
  },

  unmounted(el: Element) {
    const instance = linkageInstances.get(el)
    if (instance) {
      instance.destroy()
      linkageInstances.delete(el)
    }
  }
}

// 默认导出
export default vLinkage