<template>
  <button
    :class="buttonClassName"
    :style="{ ...computedStyle, ...buttonStyle }"
    :disabled="disabled || loading || isCountingDown"
    type="button"
    @click="handleClick"
    @mousedown="handleMouseDown"
    @mouseup="handleMouseUp"
    @mouseleave="handleMouseLeave"
    @touchstart="handleTouchStart"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchCancel"
  >
    <span
      v-if="tipConfig"
      :class="bem.e('tip')"
      :style="{
        background: tipConfig.bgColor,
        color: tipConfig.color,
        top: tipConfig.top,
        right: tipConfig.right,
        fontSize: tipConfig.fontSize,
        padding: tipConfig.padding,
      }"
    >
      {{ tipConfig.text }}
    </span>
    <span
      v-if="loading"
      :class="bem.e('loading')"
    >
      <component
        v-if="loadingIcon"
        :is="loadingIcon"
        :class="bem.e('loading-icon')"
        style="width: 1em; height: 1em"
      />
      <span
        v-else
        :class="bem.e('spinner')"
      ></span>
    </span>
    <span
      v-if="isCountingDown"
      :class="bem.e('countdown')"
    >
      {{ countdownTime }}s
    </span>
    <!-- 长按进度条 -->
    <div
      v-if="pressDown"
      :class="[
        bem.e('press-progress'),
        { [bem.em('press-progress', 'active')]: isPressing },
      ]"
    ></div>
    <span
      :class="[bem.e('content'), { [bem.em('content', 'vertical')]: vertical }]"
    >
      <component
        v-if="icon"
        :is="icon"
        :class="bem.e('icon')"
      />
      <span
        v-if="vertical"
        :class="bem.e('vertical-text')"
      >
        <slot />
      </span>
      <template v-else>
        <slot />
      </template>
    </span>
  </button>
</template>

<script setup lang="ts">
  import { ref, computed, type CSSProperties, onUnmounted } from 'vue'
  import type { ButtonProps, ButtonEmits } from './button'
  import { bemHelper } from '@speed-ui/config'
  import { createButtonClassBuilder } from './button-class-builder'

  // 创建 BEM 类名生成器
  const bem = bemHelper('button')

  // 默认属性
  const props = withDefaults(defineProps<ButtonProps>(), {
    primary: false,
    secondary: false,
    dashed: false,
    text: false,
    link: false,
    disabled: false,
    loading: false,
    loadingIcon: undefined,
    toggleable: false,
    attrStyle: () => ({}),
    size: 'medium',
    circle: false,
    type: undefined,
    icon: undefined,
    time: undefined,
    vertical: false,
    round: false,
    square: false,
    pressDown: undefined,
    tip: '',
  })

  const emit = defineEmits<ButtonEmits>()

  // 倒计时相关
  const isCountingDown = ref(false)
  const countdownTime = ref(0)
  let countdownTimer: number | null = null

  // 长按相关
  const isPressing = ref(false)
  let pressTimer: number | null = null

  const startCountdown = () => {
    if (!props.time) return
    isCountingDown.value = true
    countdownTime.value = props.time
    countdownTimer = window.setInterval(() => {
      countdownTime.value--
      emit('countdown', countdownTime.value)
      if (countdownTime.value <= 0) {
        stopCountdown()
      }
    }, 1000)
  }

  const stopCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
    isCountingDown.value = false
    countdownTime.value = 0
  }

  // 长按功能
  const startPressing = (event: MouseEvent | TouchEvent) => {
    if (
      !props.pressDown ||
      props.disabled ||
      props.loading ||
      isCountingDown.value
    )
      return

    isPressing.value = true
    emit('pressStart', event as MouseEvent)

    // 设置定时器，在指定时间后触发完成事件
    pressTimer = window.setTimeout(() => {
      if (isPressing.value) {
        emit('pressComplete', event as MouseEvent)
        stopPressing()
      }
    }, props.pressDown)
  }

  const stopPressing = () => {
    if (pressTimer) {
      clearTimeout(pressTimer)
      pressTimer = null
    }
    if (isPressing.value) {
      emit('pressEnd', new MouseEvent('mouseup'))
    }
    isPressing.value = false
  }

  // 计算CSS变量
  const buttonStyle = computed(() => {
    const style: Record<string, string> = {}
    if (props.pressDown) {
      style['--press-duration'] = `${props.pressDown}ms`
    }
    return style
  })

  onUnmounted(() => {
    stopCountdown()
    stopPressing()
  })

  // 状态管理
  const isActive = ref(false)

  // 按钮状态计算属性
  const buttonState = computed(() => ({
    isActive: isActive.value,
    isPressing: isPressing.value,
    isCountingDown: isCountingDown.value,
  }))

  // 使用类名构建器生成类名
  const buttonClassName = computed(() => {
    const builder = createButtonClassBuilder(bem, props, buttonState.value)
    return builder.build()
  })

  // 计算样式 - 只用于 attrStyle 自定义样式
  const computedStyle = computed<CSSProperties>(() => {
    const disabledStyles: CSSProperties = {
      opacity: 0.5,
      cursor: 'not-allowed',
      pointerEvents: 'none',
    }
    let mergedStyle: CSSProperties = {}
    if (props.disabled || props.loading || isCountingDown.value) {
      mergedStyle = { ...mergedStyle, ...disabledStyles }
    }
    return { ...mergedStyle, ...props.attrStyle }
  })

  // 点击处理
  const handleClick = (event: MouseEvent) => {
    if (!props.disabled && !props.loading && !isCountingDown.value) {
      // 如果有长按功能且正在长按，则不触发点击
      if (props.pressDown && isPressing.value) {
        return
      }

      if (props.toggleable) {
        isActive.value = !isActive.value
        emit('toggle', isActive.value)
      }
      emit('click', event)
      if (props.time) {
        startCountdown()
      }
    }
  }

  // 鼠标事件处理
  const handleMouseDown = (event: MouseEvent) => {
    if (props.pressDown) {
      startPressing(event)
    }
  }

  const handleMouseUp = () => {
    if (props.pressDown) {
      stopPressing()
    }
  }

  const handleMouseLeave = () => {
    if (props.pressDown) {
      stopPressing()
    }
  }

  // 触摸事件处理
  const handleTouchStart = (event: TouchEvent) => {
    if (props.pressDown) {
      event.preventDefault()
      startPressing(event)
    }
  }

  const handleTouchEnd = (event: TouchEvent) => {
    if (props.pressDown) {
      event.preventDefault()
      stopPressing()
    }
  }

  const handleTouchCancel = (event: TouchEvent) => {
    if (props.pressDown) {
      event.preventDefault()
      stopPressing()
    }
  }

  // tip 解析
  const tipConfig = computed(() => {
    if (!props.tip) return null
    if (typeof props.tip === 'string') {
      return {
        text: props.tip,
        bgColor: '#a7a2a2',
        color: '#fff',
        top: '-12px',
        right: '-8px',
        fontSize: '10px',
        padding: '1.5px 5px',
      }
    }
    return {
      bgColor: '#a7a2a2',
      color: '#fff',
      top: '-12px',
      right: '-8px',
      fontSize: '10px',
      padding: '1.5px 5px',
      ...props.tip,
      text: props.tip.text || '',
    }
  })
</script>

<style scoped>
  .sp-button {
    position: relative;
  }

  /* 自定义主题样式 */
  .sp-button-custom {
    /* 这里可以添加自定义主题的样式 */
    --button-bg: var(--sp-color-primary);
    --button-color: var(--sp-color-white);
    --button-hover-bg: var(--sp-color-primary-light);
    --button-active-bg: var(--sp-color-primary-dark);
  }

  .sp-button__tip {
    position: absolute;
    top: -12px;
    right: -8px;
    background: #a7a2a2;
    color: #fff;
    font-size: 10px;
    padding: 1.5px 5px;
    border-radius: 10px;
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    pointer-events: none;
  }

  .sp-button__loading {
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
  }

  .sp-button__loading-icon {
    animation: spin 1s linear infinite;
  }

  .sp-button__countdown {
    /* margin-right: 8px; */
    font-size: 0.9em;
    opacity: 0.8;
  }

  /* 长按进度条样式 */
  .sp-button__press-progress {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0.6) 100%
    );
    z-index: 0;
    border-radius: inherit;
    transition: none;
    overflow: hidden;
  }

  .sp-button__press-progress--active {
    animation: pressProgress var(--press-duration, 1000ms) linear forwards;
  }

  .sp-button__content {
    position: relative;
    z-index: 1;
  }

  @keyframes pressProgress {
    from {
      width: 0;
    }

    to {
      width: 100%;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
