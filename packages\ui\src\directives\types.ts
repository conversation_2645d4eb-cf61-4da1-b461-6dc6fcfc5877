import type { Directive } from 'vue'

// 指令配置接口
export interface LinkageConfig {
  /** 目标组件的ref名称，支持单个或多个目标 */
  target: string | string[]
  /** 监听的源属性名，支持嵌套属性如 'user.name' */
  when: string
  /** 要设置的目标属性名或动作对象 */
  then: string | LinkageAction
  /** 自定义条件函数，返回true时才执行联动 */
  condition?: (value: any) => boolean
  /** 值转换函数，对源值进行转换后再应用到目标 */
  transform?: (value: any) => any
  /** 延迟执行时间(毫秒)，默认为0 */
  delay?: number
}

// 动作配置接口
export interface LinkageAction {
  /** 目标属性名，支持嵌套属性 */
  prop: string
  /** 固定值，如果设置则忽略源值 */
  value?: any
  /** 值转换函数 */
  transform?: (value: any) => any
}

// 指令绑定值类型
export type LinkageBinding = LinkageConfig | LinkageConfig[]

// 扩展Vue指令类型
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    vLinkage: Directive<Element, LinkageBinding>
  }
}

// 导出指令类型
export interface LinkageDirective extends Directive<Element, LinkageBinding> {}