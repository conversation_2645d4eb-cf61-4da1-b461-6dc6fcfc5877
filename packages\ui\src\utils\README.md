# Speed UI 工具函数库

这个目录包含了 Speed UI 组件库的通用工具函数，旨在提高代码复用性和开发效率。

## 📁 文件结构

```
utils/
├── classNames.ts      # 类名处理工具
├── componentBase.ts   # 组件基础功能
├── eventHandlers.ts   # 事件处理工具
├── styleUtils.ts      # 样式工具函数
├── validation.ts      # 验证工具函数
├── index.ts          # 统一导出
└── README.md         # 说明文档
```

## 🛠️ 工具函数分类

### 1. 类名工具 (classNames.ts)

提供类名合并和BEM风格类名生成功能。

```typescript
import { classNames, getPrefixCls, bemClass } from '@speed-ui/ui/utils'

// 类名合并
const className = classNames('btn', { active: true }, ['large'])
// 结果: 'btn active large'

// 生成带前缀的类名
const prefixedClass = getPrefixCls('sp-button', 'primary')
// 结果: 'sp-button-primary'

// BEM风格类名
const bemClassName = bemClass('button', 'icon', 'large')
// 结果: 'button__icon--large'
```

### 2. 组件基础功能 (componentBase.ts)

提供组件开发中的通用功能，包括表单集成、状态管理等。

```typescript
import { 
  useFormField, 
  useComponentState, 
  generateComponentClasses 
} from '@speed-ui/ui/utils'

// 在组件中使用表单集成
const { 
  computedValidateState, 
  computedValidateMessage, 
  handleValueUpdate 
} = useFormField(props, emit)

// 使用组件状态管理
const { 
  isFocused, 
  isLoading, 
  setFocused, 
  setLoading 
} = useComponentState()

// 生成组件类名
const classes = generateComponentClasses('sp-input', props, {
  isFocused: isFocused.value,
  isLoading: isLoading.value
})
```

### 3. 事件处理工具 (eventHandlers.ts)

提供标准化的事件处理器和工具函数。

```typescript
import { 
  debounce, 
  throttle, 
  createFocusHandler, 
  EventHandlerFactory 
} from '@speed-ui/ui/utils'

// 防抖函数
const debouncedSearch = debounce((query: string) => {
  // 搜索逻辑
}, 300)

// 节流函数
const throttledScroll = throttle(() => {
  // 滚动处理逻辑
}, 100)

// 创建标准化的事件处理器
const factory = new EventHandlerFactory({
  emit,
  setState: (key, value) => { /* 状态更新逻辑 */ },
  updateValue: (value) => { /* 值更新逻辑 */ }
})

const handlers = factory.createHandlers({
  disabled: props.disabled,
  loading: props.loading
})
```

### 4. 样式工具 (styleUtils.ts)

提供样式处理和生成的工具函数。

```typescript
import { 
  mergeStyles, 
  createSizeStyle, 
  getThemeColor, 
  createStyleBuilder 
} from '@speed-ui/ui/utils'

// 合并样式对象
const mergedStyle = mergeStyles(
  { color: 'red' },
  { fontSize: '14px' },
  props.attrStyle
)

// 根据尺寸创建样式
const sizeStyle = createSizeStyle('large', 'height', {
  small: '24px',
  medium: '32px',
  large: '40px'
})

// 获取主题颜色
const primaryColor = getThemeColor('primary')
const primaryHover = getThemeColor('primary', 'hover')

// 使用样式构建器
const style = createStyleBuilder()
  .size('100%', '40px')
  .padding(8, 16)
  .color('#333')
  .backgroundColor('#fff')
  .build()
```

### 5. 验证工具 (validation.ts)

提供表单验证相关的工具函数。

```typescript
import { 
  validateValue, 
  createValidationRules, 
  Rules 
} from '@speed-ui/ui/utils'

// 创建验证规则
const emailRules = createValidationRules()
  .required('邮箱不能为空')
  .email('请输入有效的邮箱地址')
  .build()

// 验证值
const result = validateValue('<EMAIL>', emailRules)
if (!result.valid) {
  console.log(result.message) // 错误消息
}

// 使用预定义规则
const passwordRules = Rules.password(8, true) // 最少8位，必填
const phoneRules = Rules.phone(true) // 手机号，必填
```

## 🎯 使用场景

### 组件开发

在开发新组件时，可以使用这些工具函数来快速实现标准功能：

```typescript
// MyComponent.vue
<script setup lang="ts">
import { 
  useFormField, 
  useComponentState, 
  generateComponentClasses,
  EventHandlerFactory
} from '@speed-ui/ui/utils'

interface Props {
  value?: string
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  validateState?: 'success' | 'warning' | 'error'
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  disabled: false
})

const emit = defineEmits<{
  'update:value': [value: string]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

// 表单集成
const { computedValidateState, handleValueUpdate } = useFormField(props, emit)

// 状态管理
const { isFocused, setFocused } = useComponentState()

// 事件处理
const eventFactory = new EventHandlerFactory({
  emit,
  setState: (key, value) => {
    if (key === 'focused') setFocused(value)
  },
  updateValue: handleValueUpdate
})

const handlers = eventFactory.createHandlers({
  disabled: props.disabled
})

// 类名生成
const classes = computed(() => 
  generateComponentClasses('sp-my-component', props, {
    isFocused: isFocused.value
  })
)
</script>
```

### 样式定制

使用样式工具函数来创建响应式和主题化的样式：

```typescript
// 在组件中使用
const computedStyle = computed(() => {
  return createStyleBuilder()
    .add(createSizeStyle(props.size, 'height', {
      small: '24px',
      medium: '32px', 
      large: '40px'
    }))
    .add({
      color: getThemeColor('primary'),
      border: `1px solid ${getThemeColor('primary', 'light')}`
    })
    .add(props.attrStyle)
    .build()
})
```

## 🔧 扩展指南

### 添加新的工具函数

1. 在对应的文件中添加新函数
2. 添加完整的 TypeScript 类型定义
3. 添加详细的 JSDoc 注释
4. 在 `index.ts` 中导出
5. 更新此 README 文档

### 最佳实践

1. **保持函数纯净**：工具函数应该是纯函数，避免副作用
2. **类型安全**：使用 TypeScript 提供完整的类型定义
3. **文档完整**：每个函数都应该有清晰的注释和使用示例
4. **测试覆盖**：为工具函数编写单元测试
5. **向后兼容**：修改现有函数时要考虑向后兼容性

## 📝 注意事项

1. 这些工具函数主要为 Speed UI 组件库内部使用设计
2. 部分函数依赖 Vue 3 的 Composition API
3. 样式相关函数使用 CSS 变量，需要配合主题系统使用
4. 验证工具函数支持国际化，可以自定义错误消息

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这些工具函数！