<template>
  <BaseButton
    ref="baseButtonRef"
    :class="buttonClassName"
    :style="{ ...computedStyle, ...buttonStyle }"
    v-bind="$props"
    @click="handleClick"
    @toggle="handleToggle"
    @countdown="handleCountdown"
    @press-start="handlePressStart"
    @press-end="handlePressEnd"
    @press-complete="handlePressComplete"
  >
    <!-- tip 插槽 -->
    <template #tip="{ tipConfig }">
      <span
        :class="bem.e('tip')"
        :style="{
          background: tipConfig.bgColor,
          color: tipConfig.color,
          top: tipConfig.top,
          right: tipConfig.right,
          fontSize: tipConfig.fontSize,
          padding: tipConfig.padding,
        }"
      >
        {{ tipConfig.text }}
      </span>
    </template>

    <!-- loading 插槽 -->
    <template #loading>
      <span :class="bem.e('loading')">
        <component
          v-if="loadingIcon"
          :is="loadingIcon"
          :class="bem.e('loading-icon')"
          style="width: 1em; height: 1em"
        />
        <span
          v-else
          :class="bem.e('spinner')"
        ></span>
      </span>
    </template>

    <!-- countdown 插槽 -->
    <template #countdown="{ countdownTime }">
      <span :class="bem.e('countdown')">{{ countdownTime }}s</span>
    </template>

    <!-- 长按进度条插槽 -->
    <template #press-progress="{ isPressing }">
      <div
        :class="[
          bem.e('press-progress'),
          { [bem.em('press-progress', 'active')]: isPressing },
        ]"
      ></div>
    </template>

    <!-- 内容插槽 -->
    <template #content>
      <span
        :class="[
          bem.e('content'),
          { [bem.em('content', 'vertical')]: vertical },
        ]"
      >
        <component
          v-if="icon"
          :is="icon"
          :class="bem.e('icon')"
        />
        <span
          v-if="vertical"
          :class="bem.e('vertical-text')"
        >
          <slot />
        </span>
        <template v-else>
          <slot />
        </template>
      </span>
    </template>
  </BaseButton>
</template>

<script setup lang="ts">
  import { ref, computed, type CSSProperties } from 'vue'
  import BaseButton from '../base-button/base-button.vue'
  import type { ButtonProps, ButtonEmits } from './button'
  import { bemHelper } from '@speed-ui/config'
  import { createButtonClassBuilder } from './button-class-builder'

  // 创建 BEM 类名生成器
  const bem = bemHelper('button')

  // 使用完整的 ButtonProps
  const props = withDefaults(defineProps<ButtonProps>(), {
    primary: false,
    secondary: false,
    dashed: false,
    text: false,
    link: false,
    disabled: false,
    loading: false,
    loadingIcon: undefined,
    toggleable: false,
    attrStyle: () => ({}),
    size: 'medium',
    circle: false,
    type: undefined,
    icon: undefined,
    time: undefined,
    vertical: false,
    round: false,
    square: false,
    pressDown: undefined,
    tip: '',
  })

  const emit = defineEmits<ButtonEmits>()

  const baseButtonRef = ref<InstanceType<typeof BaseButton>>()

  // 使用类名构建器生成类名
  const buttonClassName = computed(() => {
    // 从 base-button 获取状态
    const buttonState = baseButtonRef.value?.buttonState?.value || {
      isActive: false,
      isPressing: false,
      isCountingDown: false,
    }

    const builder = createButtonClassBuilder(bem, props, buttonState)
    return builder.build()
  })

  // 计算CSS变量
  const buttonStyle = computed(() => {
    const style: Record<string, string> = {}
    if (props.pressDown) {
      style['--press-duration'] = `${props.pressDown}ms`
    }
    return style
  })

  // 计算样式 - 只用于 attrStyle 自定义样式
  const computedStyle = computed<CSSProperties>(() => {
    const disabledStyles: CSSProperties = {
      opacity: 0.5,
      cursor: 'not-allowed',
      pointerEvents: 'none',
    }
    let mergedStyle: CSSProperties = {}
    if (props.disabled || props.loading) {
      mergedStyle = { ...mergedStyle, ...disabledStyles }
    }
    return { ...mergedStyle, ...props.attrStyle }
  })

  // 事件处理 - 直接转发
  const handleClick = (event: MouseEvent) => {
    emit('click', event)
  }

  const handleToggle = (active: boolean) => {
    emit('toggle', active)
  }

  const handleCountdown = (time: number) => {
    emit('countdown', time)
  }

  const handlePressStart = (event: MouseEvent) => {
    emit('pressStart', event)
  }

  const handlePressEnd = (event: MouseEvent) => {
    emit('pressEnd', event)
  }

  const handlePressComplete = (event: MouseEvent) => {
    emit('pressComplete', event)
  }

  // 暴露方法
  defineExpose({
    focus: () => baseButtonRef.value?.focus?.(),
    blur: () => baseButtonRef.value?.blur?.(),
    startCountdown: () => baseButtonRef.value?.startCountdown?.(),
    stopCountdown: () => baseButtonRef.value?.stopCountdown?.(),
  })
</script>

<style scoped>
  .sp-button {
    position: relative;
  }

  /* 自定义主题样式 */
  .sp-button-custom {
    /* 这里可以添加自定义主题的样式 */
    --button-bg: var(--sp-color-primary);
    --button-color: var(--sp-color-white);
    --button-hover-bg: var(--sp-color-primary-light);
    --button-active-bg: var(--sp-color-primary-dark);
  }

  .sp-button__tip {
    position: absolute;
    top: -12px;
    right: -8px;
    background: #a7a2a2;
    color: #fff;
    font-size: 10px;
    padding: 1.5px 5px;
    border-radius: 10px;
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    pointer-events: none;
  }

  .sp-button__loading {
    display: inline-flex;
    align-items: center;
    margin-right: 8px;
  }

  .sp-button__loading-icon {
    animation: spin 1s linear infinite;
  }

  .sp-button__countdown {
    font-size: 0.9em;
    opacity: 0.8;
  }

  /* 长按进度条样式 */
  .sp-button__press-progress {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 255, 255, 0.4) 50%,
      rgba(255, 255, 255, 0.6) 100%
    );
    z-index: 0;
    border-radius: inherit;
    transition: none;
    overflow: hidden;
  }

  .sp-button__press-progress--active {
    animation: pressProgress var(--press-duration, 1000ms) linear forwards;
  }

  .sp-button__content {
    position: relative;
    z-index: 1;
  }

  @keyframes pressProgress {
    from {
      width: 0;
    }

    to {
      width: 100%;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }
</style>
