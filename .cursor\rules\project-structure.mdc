---
description:
globs:
alwaysApply: false
---
# 项目结构指南

这是一个基于 pnpm workspace 的 monorepo 项目，主要包含以下目录结构：

## 核心目录

- `packages/` - 核心包目录
  - `ui/` - UI 组件库
  - `theme-default/` - 默认主题包
  - `utils/` - 工具函数包

## 其他重要目录

- `docs/` - 项目文档
- `playground/` - 组件演示和测试环境
- `build/` - 构建相关配置和脚本

## 配置文件

- `pnpm-workspace.yaml` - 工作空间配置
- `tsconfig.json` - TypeScript 配置
- `.eslintrc.js` - ESLint 配置
- `package.json` - 项目依赖和脚本

## 开发流程

1. 组件开发在 `packages/ui/src/components` 目录下进行
2. 使用 `playground` 目录进行组件测试和预览
3. 文档更新在 `docs` 目录下进行
4. 使用 `.changeset` 目录管理版本变更
