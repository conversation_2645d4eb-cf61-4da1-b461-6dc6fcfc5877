<template>
  <div class="input-number-test">
    <h1>🔢 数字输入框测试</h1>
    <p>测试数字输入框的控制按钮（spinner）功能</p>

    <div class="demo-section">
      <h2>基础数字输入框</h2>
      
      <div class="demo-item">
        <label>基础用法:</label>
        <sp-input-number
          v-model:value="basicValue"
          placeholder="请输入数字"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ basicValue }}</span>
      </div>

      <div class="demo-item">
        <label>设置步长:</label>
        <sp-input-number
          v-model:value="stepValue"
          :step="0.1"
          placeholder="步长 0.1"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ stepValue }}</span>
      </div>

      <div class="demo-item">
        <label>最大最小值:</label>
        <sp-input-number
          v-model:value="rangeValue"
          :min="0"
          :max="100"
          placeholder="0-100"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ rangeValue }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>精度控制</h2>
      
      <div class="demo-item">
        <label>保留2位小数:</label>
        <sp-input-number
          v-model:value="precisionValue"
          :precision="2"
          :step="0.01"
          placeholder="精度2位"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ precisionValue }}</span>
      </div>

      <div class="demo-item">
        <label>仅整数:</label>
        <sp-input-number
          v-model:value="integerValue"
          :allow-decimal="false"
          placeholder="仅整数"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ integerValue }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>不同尺寸</h2>
      
      <div class="demo-item">
        <label>小尺寸:</label>
        <sp-input-number
          v-model:value="smallValue"
          size="small"
          placeholder="小尺寸"
          style="width: 150px;"
        />
        <span class="value-display">值: {{ smallValue }}</span>
      </div>

      <div class="demo-item">
        <label>中等尺寸:</label>
        <sp-input-number
          v-model:value="mediumValue"
          size="medium"
          placeholder="中等尺寸"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ mediumValue }}</span>
      </div>

      <div class="demo-item">
        <label>大尺寸:</label>
        <sp-input-number
          v-model:value="largeValue"
          size="large"
          placeholder="大尺寸"
          style="width: 250px;"
        />
        <span class="value-display">值: {{ largeValue }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>禁用状态</h2>
      
      <div class="demo-item">
        <label>禁用状态:</label>
        <sp-input-number
          v-model:value="disabledValue"
          disabled
          placeholder="禁用状态"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ disabledValue }}</span>
      </div>

      <div class="demo-item">
        <label>只读状态:</label>
        <sp-input-number
          v-model:value="readonlyValue"
          readonly
          placeholder="只读状态"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ readonlyValue }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>控制按钮选项</h2>
      
      <div class="demo-item">
        <label>隐藏控制按钮:</label>
        <sp-input-number
          v-model:value="noControlsValue"
          :controls="false"
          placeholder="无控制按钮"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ noControlsValue }}</span>
      </div>

      <div class="demo-item">
        <label>
          <input 
            type="checkbox" 
            v-model="dynamicControls"
          />
          动态控制按钮
        </label>
        <sp-input-number
          v-model:value="dynamicValue"
          :controls="dynamicControls"
          placeholder="动态控制"
          style="width: 200px;"
        />
        <span class="value-display">值: {{ dynamicValue }}</span>
      </div>
    </div>

    <div class="demo-section">
      <h2>功能说明</h2>
      <div class="feature-info">
        <h3>控制按钮功能：</h3>
        <ul>
          <li>✅ 点击上箭头增加数值</li>
          <li>✅ 点击下箭头减少数值</li>
          <li>✅ 支持自定义步长（step）</li>
          <li>✅ 支持最大最小值限制</li>
          <li>✅ 支持精度控制</li>
          <li>✅ 禁用状态下控制按钮不可用</li>
          <li>✅ 只读状态下控制按钮不可用</li>
          <li>✅ 可以完全隐藏控制按钮</li>
          <li>✅ 响应式设计，适配不同尺寸</li>
        </ul>
        
        <h3>键盘支持：</h3>
        <ul>
          <li>🔼 上箭头键：增加数值</li>
          <li>🔽 下箭头键：减少数值</li>
          <li>⌨️ 直接输入数字</li>
          <li>🚫 自动过滤非数字字符</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 测试数据
const basicValue = ref(0)
const stepValue = ref(1.0)
const rangeValue = ref(50)
const precisionValue = ref(3.14)
const integerValue = ref(10)

const smallValue = ref(1)
const mediumValue = ref(2)
const largeValue = ref(3)

const disabledValue = ref(100)
const readonlyValue = ref(200)

const noControlsValue = ref(5)
const dynamicValue = ref(10)
const dynamicControls = ref(true)
</script>

<style scoped>
.input-number-test {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: #fafafa;
}

.demo-section h2 {
  margin-top: 0;
  color: #409eff;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.demo-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.demo-item label {
  min-width: 150px;
  font-weight: 500;
  color: #606266;
}

.value-display {
  min-width: 100px;
  padding: 4px 8px;
  background-color: #f0f9ff;
  border: 1px solid #b3d8ff;
  border-radius: 4px;
  font-size: 12px;
  color: #409eff;
}

.feature-info {
  background-color: #f0f9ff;
  padding: 16px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.feature-info h3 {
  margin-top: 0;
  color: #409eff;
}

.feature-info ul {
  margin: 0;
  padding-left: 20px;
}

.feature-info li {
  margin-bottom: 8px;
  color: #606266;
}
</style>
