<template>
  <div>
    <!-- ✅ 正确用法：使用 v-model:value -->
    <sp-select
      v-model:value="selectedValue"
      :options="options"
      placeholder="请选择一个选项"
    />
    
    <!-- ❌ 不支持：v-model -->
    <!-- <sp-select v-model="selectedValue" :options="options" /> -->
    
    <p>选中的值：{{ selectedValue }}</p>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref(null)
const options = [
  { label: '选项一', value: 1 },
  { label: '选项二', value: 2 },
  { label: '选项三', value: 3 }
]
</script>
