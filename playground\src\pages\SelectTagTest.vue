<template>
  <div class="select-tag-test">
    <h1>Select Tag 测试</h1>

    <div class="demo-section">
      <h3>🏷️ 多选 Tag 显示测试</h3>

      <div class="demo-item">
        <label>基础多选（Tag 形式）：</label>
        <sp-select
          v-model:value="multipleValue"
          :options="options"
          placeholder="请选择多个选项"
          multiple
          clearable
          style="width: 400px"
        />
        <div class="value-display">
          选中值: {{ multipleValue.length ? multipleValue.join(', ') : '无' }}
        </div>
      </div>

      <div class="demo-item">
        <label>单选对比：</label>
        <sp-select
          v-model:value="singleValue"
          :options="options"
          placeholder="请选择一个选项"
          clearable
          style="width: 400px"
        />
        <div class="value-display">选中值: {{ singleValue || '无' }}</div>
      </div>

      <div class="demo-item">
        <label>多选（更多选项）：</label>
        <sp-select
          v-model:value="manyValue"
          :options="manyOptions"
          placeholder="请选择多个选项"
          multiple
          clearable
          style="width: 500px"
        />
        <div class="value-display">
          选中值: {{ manyValue.length ? manyValue.join(', ') : '无' }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import { Select as SpSelect } from '../../../packages/ui/src/components/select'

  // 测试数据
  const multipleValue = ref(['option1', 'option3'])
  const singleValue = ref('option2')
  const manyValue = ref(['js', 'vue', 'ts'])

  // 添加一些调试信息
  console.log('🔍 初始化测试数据:', {
    multipleValue: multipleValue.value,
    singleValue: singleValue.value,
    manyValue: manyValue.value,
  })

  // 基础选项
  const options = [
    { label: '选项一', value: 'option1' },
    { label: '选项二', value: 'option2' },
    { label: '选项三', value: 'option3' },
    { label: '选项四', value: 'option4' },
    { label: '选项五', value: 'option5' },
  ]

  // 更多选项
  const manyOptions = [
    { label: 'JavaScript', value: 'js' },
    { label: 'TypeScript', value: 'ts' },
    { label: 'Vue.js', value: 'vue' },
    { label: 'React', value: 'react' },
    { label: 'Angular', value: 'angular' },
    { label: 'Node.js', value: 'nodejs' },
    { label: 'Python', value: 'python' },
    { label: 'Java', value: 'java' },
  ]
</script>

<style scoped>
  .select-tag-test {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
      sans-serif;
  }

  .select-tag-test h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
  }

  .demo-section {
    background: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e4e7ed;
  }

  .demo-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #606266;
    font-size: 18px;
  }

  .demo-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 30px;
  }

  .demo-item:last-child {
    margin-bottom: 0;
  }

  .demo-item label {
    font-weight: 600;
    color: #606266;
    font-size: 14px;
  }

  .value-display {
    color: #909399;
    font-size: 12px;
    background: #f5f7fa;
    padding: 8px 12px;
    border-radius: 4px;
    margin-top: 8px;
  }
</style>
